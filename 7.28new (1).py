import numpy as np # 导入NumPy库，用于进行高效的科学计算和矩阵运算
import pandas as pd # 导入Pandas库，用于数据处理、分析和CSV文件读写
import matplotlib.pyplot as plt # 导入Matplotlib库，用于数据可视化和绘图
from mpl_toolkits.mplot3d import Axes3D # 从Matplotlib导入Axes3D，用于绘制三维图形
from scipy.constants import c # 从scipy.constants导入物理常量光速c
import time # 导入time库，用于测量代码执行时间
from tqdm import tqdm # 导入tqdm库，用于在循环中显示美观的进度条
import os # 导入os库，用于处理文件和目录路径
import matplotlib.animation as animation # 导入animation模块，用于创建动画
import matplotlib.pyplot as plt
import matplotlib
# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 或者使用这种方式
try:
    matplotlib.font_manager.fontManager.addfont('C:/Windows/Fonts/simhei.ttf')
    plt.rcParams['font.family'] = ['SimHei']
except:
    # 如果没有中文字体，使用英文
    plt.rcParams['font.family'] = ['DejaVu Sans']

# --- TDOA求解器全局参数配置 ---
# TDOA_SOLVER_PARAMS = {
#     'max_iter': 50,        # 最大迭代次数
#     'tol': 1e-6,           # 收敛容忍度
#     'lambda_reg': 1e-3,    # 牛顿法正则化参数
#     'lambda_init': 1e-3,   # LM算法初始阻尼因子
#     'lambda_up': 10,       # LM算法阻尼因子增大倍数
#     'lambda_down': 0.1     # LM算法阻尼因子减小倍数
# }
TDOA_SOLVER_PARAMS = {
    'max_iter': 100,
    'tol': 1e-8,
    'lambda_reg': 1e-4,
    'lambda_init': 1e-4,
    'lambda_up': 5,
    'lambda_down': 0.2
}

# --- 数据加载 ---
def load_data(file_path):
    """
    从指定的CSV文件加载仿真数据
    
    参数:
        file_path (str): CSV文件的路径。
    
    返回:
        tuple: 包含以下元素的元组：
            - monitor_coords (np.ndarray): 监测站的三维坐标数组，形状为 (6, 3)。
            - uav_true_positions (dict): 包含每个无人机真实轨迹的字典，键为无人机ID（如'UAV1'），值为轨迹坐标数组（形状为 [n_steps, 3]）。
            - timestamps (np.ndarray): 时间戳数组。
            - formation_labels (np.ndarray): 阵型标签数组。
    """
    df = pd.read_csv(file_path) # 使用pandas读取CSV文件到DataFrame
    
    # 提取6个监测站的坐标
    monitor_coords = [] # 初始化列表用于存储监测站坐标
    for i in range(1, 7): # 循环遍历6个监测站
        x = df[f'Monitor{i}_X'].iloc[0] # 获取第i个监测站的X坐标 (只取第一行，因为是固定站)
        y = df[f'Monitor{i}_Y'].iloc[0] # 获取第i个监测站的Y坐标
        z = df[f'Monitor{i}_Z'].iloc[0] # 获取第i个监测站的Z坐标
        monitor_coords.append([x, y, z]) # 将坐标列表添加到monitor_coords中
    monitor_coords = np.array(monitor_coords) # 将列表转换为NumPy数组

    # 提取所有目标无人机的ID和轨迹
    # 通过列名中是否包含 'UAV' 来识别无人机数据
    uav_ids = [col.split('_')[0] for col in df.columns if 'UAV' in col]
    uav_ids = sorted(list(set(uav_ids))) # 去重并排序，得到所有无人机的唯一ID列表
    
    uav_true_positions = {} # 初始化字典用于存储每个无人机的轨迹
    for uav in uav_ids: # 遍历每个无人机ID
        # 提取该无人机在所有时间点的X, Y, Z坐标
        uav_true_positions[uav] = df[[f'{uav}_X', f'{uav}_Y', f'{uav}_Z']].values

    # 提取阵型标签
    formation_labels = df['Formation_Label'].values if 'Formation_Label' in df.columns else None

    return monitor_coords, uav_true_positions, df['timestamp'].values, formation_labels

# --- TDOA 定位算法核心 ---
def tdoa_residuals(est_pos, anchors, tdoa_meas, ref_idx=0):
    """
    计算TDOA定位中的残差向量。
    残差定义为：(估计位置到各站的距离差) - (TDOA测量距离差)。
    这个向量是牛顿迭代法等优化算法的目标函数。

    参数:
        est_pos (np.ndarray): 目标当前被估计的三维位置 [x, y, z]。
        anchors (np.ndarray): 所有观测站（基站）的三维坐标数组。
        tdoa_meas (np.ndarray): TDOA测量值（以距离差表示），此数组不包含参考站自身。
        ref_idx (int): 参考站在anchors数组中的索引。

    返回:
        np.ndarray: 残差向量。
    """
    # 计算估计位置到所有观测站的欧氏距离
    est_dist = np.linalg.norm(anchors - est_pos, axis=1)
    # 计算基于估计位置的TDOA值（相对于参考站的距离差）
    est_tdoa = est_dist - est_dist[ref_idx]
    
    # 从估计的TDOA中删除参考站对应的值(为0)，使其与测量值tdoa_meas的维度匹配
    est_tdoa_without_ref = np.delete(est_tdoa, ref_idx)
    
    # 返回估计TDOA与实际测量TDOA之间的差值，即残差
    return est_tdoa_without_ref - tdoa_meas

def jacobian_matrix(est_pos, anchors, ref_idx=0):
    """
    解析法计算TDOA残差函数相对于目标位置(x, y, z)的雅可比矩阵。
    雅可比矩阵在牛顿迭代法中用于确定下一步的迭代方向和步长。
    优化版本：使用矢量化操作避免循环。

    参数:
        est_pos (np.ndarray): 目标当前被估计的三维位置 [x, y, z]。
        anchors (np.ndarray): 所有观测站（基站）的三维坐标数组。
        ref_idx (int): 参考站在anchors数组中的索引。

    返回:
        np.ndarray: 雅可比矩阵，形状为 (n_anchors - 1, 3)。
    """
    # 矢量化计算所有距离
    diffs = est_pos - anchors  # (n_anchors, 3)
    est_dist = np.linalg.norm(diffs, axis=1)  # (n_anchors,)

    # 避免除零
    est_dist = np.maximum(est_dist, 1e-12)

    # 计算所有导数
    derivs = diffs / est_dist[:, np.newaxis]  # (n_anchors, 3)

    # 参考站导数
    ref_deriv = derivs[ref_idx]  # (3,)

    # 构建雅可比矩阵，排除参考站
    mask = np.arange(len(anchors)) != ref_idx
    J = derivs[mask] - ref_deriv  # (n_anchors-1, 3)

    return J

def newton_tdoa_solver(anchors, tdoa_meas, initial_guess, ref_idx=0,
                      max_iter=None, tol=None, lambda_reg=None):
    """
    使用牛顿迭代法（高斯-牛顿法）求解TDOA定位问题。

    参数:
        anchors (np.ndarray): 所有观测站的坐标。
        tdoa_meas (np.ndarray): TDOA测量值（距离差）。
        initial_guess (np.ndarray): 初始猜测的目标位置。
        ref_idx (int): 参考站索引。
        max_iter (int): 最大迭代次数。
        tol (float): 两次迭代误差变化小于该值时认为收敛。
        lambda_reg (float): 正则化项（阻尼因子），用于提高解的稳定性，防止矩阵奇异。

    返回:
        np.ndarray: 最终估计的目标位置 [x, y, z]。
    """
    # 使用全局参数作为默认值
    global TDOA_SOLVER_PARAMS
    if max_iter is None:
        max_iter = TDOA_SOLVER_PARAMS['max_iter']
    if tol is None:
        tol = TDOA_SOLVER_PARAMS['tol']
    if lambda_reg is None:
        lambda_reg = TDOA_SOLVER_PARAMS['lambda_reg']

    pos = np.array(initial_guess, dtype=np.float64).copy() # 确保数据类型为float64
    prev_error = np.inf # 初始化上一次迭代的误差为无穷大
    
    for _ in range(max_iter):
        # 1. 计算当前位置的残差向量
        residuals = tdoa_residuals(pos, anchors, tdoa_meas, ref_idx)
        # 2. 计算雅可比矩阵
        J = jacobian_matrix(pos, anchors, ref_idx)
        
        # 3. 构造高斯-牛顿法的线性方程组 J^T*J * step = -J^T * residuals
        #    添加一个小的正则化项 (lambda * I) 来增加数值稳定性，这类似于Levenberg-Marquardt方法
        JTJ = J.T @ J + lambda_reg * np.eye(3) 
        
        # 4. 解线性方程组，得到位置更新步长 step
        step = np.linalg.solve(JTJ, -J.T @ residuals)
        
        # 5. 更新位置
        pos += step
        
        # 6. 检查收敛性
        current_error = np.linalg.norm(residuals) # 当前残差的L2范数作为误差
        if abs(prev_error - current_error) < tol:
            break # 如果误差变化足够小，则停止迭代
        prev_error = current_error
        
    return pos

def newton_tdoa_solver_lm(anchors, tdoa_meas, initial_guess, ref_idx=0,
                         max_iter=None, tol=None, lambda_init=None,
                         lambda_up=None, lambda_down=None):
    """
    使用Levenberg-Marquardt算法求解TDOA定位问题。
    这是一种改进的牛顿法，具有自适应阻尼因子，能够在非二次曲面上更稳健地收敛。

    主要改进点：
    1. 自适应阻尼 λ：初始化 λ = 1e-3，每次步长没降误差就放大（*10），降了就缩小（*0.1），
       可防止过冲或停滞。
    2. 折返检查：在真正更新 pos 前，先试算一次 dp 后的新误差 new_err，只有当它更小才接受
       更新并缩小 λ，否则拒绝更新并放大 λ。
    3. 收敛更稳健：折返保证残差单调下降；动态 λ 保证当接近最优时步长足够小，远离时又能快速收敛。

    算法特点：
    - 当步长导致残差增大时，会自动提高 λ，退回到"更像梯度下降"的小步长（更稳）
    - 当残差持续下降时，会降低 λ，迅速收敛到二次近似的 Newton 速度（更快）

    参数:
        anchors (np.ndarray): 所有观测站的坐标。
        tdoa_meas (np.ndarray): TDOA测量值（距离差）。
        initial_guess (np.ndarray): 初始猜测的目标位置。
        ref_idx (int): 参考站索引。
        max_iter (int): 最大迭代次数。
        tol (float): 两次迭代误差变化小于该值时认为收敛。
        lambda_init (float): 初始阻尼因子。
        lambda_up (float): 阻尼因子增大倍数。
        lambda_down (float): 阻尼因子减小倍数。

    返回:
        tuple: (最终估计的目标位置 [x, y, z], 迭代次数)
    """
    # 使用全局参数作为默认值
    global TDOA_SOLVER_PARAMS
    if max_iter is None:
        max_iter = TDOA_SOLVER_PARAMS['max_iter']
    if tol is None:
        tol = TDOA_SOLVER_PARAMS['tol']
    if lambda_init is None:
        lambda_init = TDOA_SOLVER_PARAMS['lambda_init']
    if lambda_up is None:
        lambda_up = TDOA_SOLVER_PARAMS['lambda_up']
    if lambda_down is None:
        lambda_down = TDOA_SOLVER_PARAMS['lambda_down']

    pos = np.array(initial_guess, dtype=np.float64).copy()
    lamb = lambda_init
    prev_err = np.linalg.norm(tdoa_residuals(pos, anchors, tdoa_meas, ref_idx))

    iter_count = 0
    for _ in range(max_iter):
        iter_count += 1

        # 计算当前位置的残差向量和雅可比矩阵
        r = tdoa_residuals(pos, anchors, tdoa_meas, ref_idx)
        J = jacobian_matrix(pos, anchors, ref_idx)

        # 构造Levenberg-Marquardt方程：(J^T*J + λ*I) * dp = -J^T * r
        G = J.T @ J
        H = G + lamb * np.eye(3)
        dp = -np.linalg.solve(H, J.T @ r)

        # 试探新位置并计算新误差
        new_pos = pos + dp
        new_err = np.linalg.norm(tdoa_residuals(new_pos, anchors, tdoa_meas, ref_idx))

        # 自适应调整阻尼因子
        if new_err < prev_err:
            # 误差下降，接受更新并减小阻尼因子
            error_change = abs(prev_err - new_err)
            pos = new_pos
            prev_err = new_err
            lamb = max(lamb * lambda_down, 1e-12)  # 防止λ过小

            # 检查收敛性（只有在接受更新时才检查）
            if error_change < tol:
                break
        else:
            # 误差增大，拒绝更新并增大阻尼因子
            lamb *= lambda_up
            # 如果阻尼因子过大，可能陷入困境，此时也退出
            if lamb > 1e10:
                break

    return pos, iter_count

def chan_initial(anchors, tdoa_meas, ref_idx=0):
    """
    使用Chan氏算法（一种非迭代的解析算法）为TDOA定位提供一个良好的初始解。
    该方法通过将非线性方程组转换为线性方程组来求解。

    参数:
        anchors (np.ndarray): 观测站坐标。
        tdoa_meas (np.ndarray): TDOA测量值（距离差）。
        ref_idx (int): 参考站索引。

    返回:
        np.ndarray: Chan算法计算出的初始位置估计 [x, y, z]。
    """
    ref_anchor = anchors[ref_idx] # 参考站坐标
    M = anchors.shape[0] # 观测站总数
    
    A = [] # 线性方程组的系数矩阵
    d = [] # 线性方程组的右侧向量
    
    tdoa_idx = 0
    for i in range(M):
        if i == ref_idx:
            continue
            
        ai = anchors[i] # 当前观测站i的坐标
        ti = tdoa_meas[tdoa_idx] # 对应的TDOA距离差
        tdoa_idx += 1
        
        # 构造线性方程组的一行
        A.append(np.hstack((2 * (ai - ref_anchor), -2 * ti)))
        d.append(np.sum(ai**2) - np.sum(ref_anchor**2) - ti**2)
        
    A = np.array(A)
    d = np.array(d)
    
    # 使用最小二乘法求解 Ax = d，其中未知向量x包含 [x, y, z, r_ref]
    # r_ref 是目标到参考站的距离
    u, _, _, _ = np.linalg.lstsq(A, d, rcond=None)
    
    # 提取解向量的前三个元素作为位置估计
    x_init = u[:3]
    return x_init

    """
    计算单个目标点相对于当前基站布局的几何精度因子（GDOP）。
    GDOP = sqrt(trace((J^T * J)^-1))，值越小代表定位精度越高。
    优化版本：使用矢量化操作。

    参数:
        target_pos (np.ndarray): 单个目标的位置 [x, y, z]。
        anchors (np.ndarray): 全部观测站的坐标 (n_anchors x 3)。
    返回:
        float: GDOP值。
    """
    # 矢量化计算所有距离
    diffs = target_pos - anchors  # (n_anchors, 3)
    dists = np.linalg.norm(diffs, axis=1)  # (n_anchors,)

    # 检查是否有基站与目标重合
    if np.any(dists < 1e-9):
        return np.inf

    # 矢量化计算雅可比矩阵
    J = -diffs / dists[:, np.newaxis]  # (n_anchors, 3)

    try:
        # 计算 H = J^T * J
        H = J.T @ J
        # 为增加数值稳定性，在求逆前给对角线增加一个极小量
        H_inv = np.linalg.inv(H + np.eye(H.shape[0]) * 1e-9)
        # GDOP = sqrt(trace(H_inv))
        gdop_value = np.sqrt(np.abs(np.trace(H_inv))) # 取绝对值避免浮点误差导致极小的负数
    except np.linalg.LinAlgError:
        # 如果矩阵H奇异（例如所有基站共线或共面），则GDOP为无穷大
        gdop_value = np.inf

    return gdop_value

def calculate_average_gdop(anchors, airspace_grid):
    """
    计算给定基站布局在整个监视空域网格上的平均GDOP。
    这是差分进化算法的适应度函数（目标函数），我们需要最小化这个值。

    参数:
        anchors (np.ndarray): 全部观测站的坐标 (n_anchors x 3)。
        airspace_grid (np.ndarray): 监视空域的离散网格点坐标 (N_points x 3)。
    返回:
        float: 平均GDOP值。
    """
    # 使用NumPy的广播和矢量化操作来高效计算所有网格点的GDOP
    diffs = airspace_grid[:, np.newaxis, :] - anchors[np.newaxis, :, :]
    dists = np.linalg.norm(diffs, axis=2)
    
    if np.any(dists < 1e-9): # 检查是否有网格点与基站重合
        return np.inf
        
    # J的形状是 (N_points, n_anchors, 3)
    J = -diffs / dists[..., np.newaxis]
    
    # 一次性计算所有点的 H = J^T * J
    # 'nij,nik->njk' 表示对每个点n，计算J[n,:,:]^T @ J[n,:,:]
    H = np.einsum('nij,nik->njk', J, J) 
    
    try:
        # 一次性计算所有H矩阵的逆
        H_inv = np.linalg.inv(H + np.eye(H.shape[1]) * 1e-9)
        # 一次性计算所有逆矩阵的迹并求平方根
        # 'nii->n' 表示对每个点n，计算H_inv[n,:,:]的迹
        gdop_values = np.sqrt(np.abs(np.einsum('nii->n', H_inv)))
        return np.mean(gdop_values) # 返回所有网格点GDOP的平均值
    except np.linalg.LinAlgError:
        return np.inf

def _is_in_forbidden_zone(uav_positions, forbidden_zones):
    """
    辅助函数：检查一组无人机位置是否进入了任何一个禁飞区。
    """
    for zone in forbidden_zones:
        if zone['type'] == 'box':
            bounds = zone['bounds'] # [[x_min, y_min, z_min], [x_max, y_max, z_max]]
            for pos in uav_positions:
                if (bounds[0][0] <= pos[0] <= bounds[1][0] and
                    bounds[0][1] <= pos[1] <= bounds[1][1] and
                    bounds[0][2] <= pos[2] <= bounds[1][2]):
                    return True # 只要有一个无人机在禁飞区内，就返回True
    return False

def run_adaptive_de_optimization(fixed_anchors, initial_uav_positions, de_params, constraints, airspace_grid):
    """
    使用自适应差分进化算法优化无人机观测站位置
    """
    # 解包参数
    NP = de_params['NP']
    G_max = de_params['G_max']
    F0 = de_params['F0']
    patience = de_params['patience']
    tolerance = de_params['tolerance']
    
    # --- 1. 初始化种群 ---
    search_space = constraints['search_space']
    forbidden_zones = constraints.get('forbidden_zones', [])
    
    population = np.zeros((NP, 6))  # 2个无人机 × 3个坐标
    for i in range(NP):
        while True:
            candidate = np.array([
                np.random.uniform(search_space['x'][0], search_space['x'][1]),
                np.random.uniform(search_space['y'][0], search_space['y'][1]),
                np.random.uniform(search_space['z'][0], search_space['z'][1]),
                np.random.uniform(search_space['x'][0], search_space['x'][1]),
                np.random.uniform(search_space['y'][0], search_space['y'][1]),
                np.random.uniform(search_space['z'][0], search_space['z'][1])
            ])
            uav_positions = candidate.reshape(2, 3)
            if not _is_in_forbidden_zone(uav_positions, forbidden_zones):
                population[i] = candidate
                break
    
    # 初始化适应度和历史记录
    fitness = np.full(NP, np.inf)
    fitness_history = []
    best_fitness_so_far = np.inf
    best_solution = population[0]
    generations_run = 0
    stop_reason = "Max generations reached"
    
    # --- 2. DE主循环（不显示进度条，避免干扰主进度条） ---
    for g in range(G_max):
        generations_run += 1
        
        # a. 评估种群中每个个体的适应度
        for i in range(NP):
            uav_pos = population[i].reshape(2, 3)
            current_anchors = np.vstack((fixed_anchors, uav_pos))
            fitness[i] = calculate_average_gdop(current_anchors, airspace_grid)

        # b. 记录当前代的最佳结果并检查是否收敛
        best_idx = np.argmin(fitness)
        if fitness[best_idx] < best_fitness_so_far:
            best_fitness_so_far = fitness[best_idx]
            best_solution = population[best_idx]
        
        fitness_history.append(best_fitness_so_far)
        
        # 早停机制
        if g > patience:
            if (fitness_history[-patience] - best_fitness_so_far) < tolerance:
                stop_reason = "Converged"
                break
        
        # c. 自适应更新DE的控制参数
        lambda_val = np.exp(1 - G_max / (G_max + 1 - (g + 1)))
        F = F0 * (2 ** lambda_val)
        CR = 0.5 * (1 + np.random.rand())
        
        # d. 变异、交叉和选择
        new_population = population.copy()
        for i in range(NP):
            # 变异
            indices = np.random.choice([j for j in range(NP) if j != i], 3, replace=False)
            mutant_vec = population[indices[0]] + F * (population[indices[1]] - population[indices[2]])
            
            # 交叉
            trial_vec = population[i].copy()
            for j in range(6):
                if np.random.rand() < CR:
                    trial_vec[j] = mutant_vec[j]
            
            # 边界处理
            trial_vec[0] = np.clip(trial_vec[0], search_space['x'][0], search_space['x'][1])
            trial_vec[1] = np.clip(trial_vec[1], search_space['y'][0], search_space['y'][1])
            trial_vec[2] = np.clip(trial_vec[2], search_space['z'][0], search_space['z'][1])
            trial_vec[3] = np.clip(trial_vec[3], search_space['x'][0], search_space['x'][1])
            trial_vec[4] = np.clip(trial_vec[4], search_space['y'][0], search_space['y'][1])
            trial_vec[5] = np.clip(trial_vec[5], search_space['z'][0], search_space['z'][1])
            
            # 选择
            trial_uavs = trial_vec.reshape(2, 3)
            if not _is_in_forbidden_zone(trial_uavs, forbidden_zones):
                trial_anchors = np.vstack((fixed_anchors, trial_uavs))
                trial_fitness = calculate_average_gdop(trial_anchors, airspace_grid)
                if trial_fitness < fitness[i]:
                    new_population[i] = trial_vec
        
        population = new_population
    
    # --- 3. 返回结果 ---
    results = {
        'best_positions': best_solution.reshape(2, 3),
        'best_fitness': best_fitness_so_far,
        'fitness_history': fitness_history,
        'generations_run': generations_run,
        'stop_reason': stop_reason
    }
    
    return results

# --- 辅助与仿真流程函数 ---
def calculate_tdoa_measurements(anchors, true_pos, ref_idx=0, noise_std=0.0):
    """
    根据目标真实位置和观测站布局，模拟生成TDOA测量值，并可选择添加高斯噪声。

    参数:
        anchors (np.ndarray): 观测站坐标。
        true_pos (np.ndarray): 目标的真实位置。
        ref_idx (int): 参考站索引。
        noise_std (float): 添加到TDOA距离差上的高斯噪声的标准差（单位：米）。

    返回:
        np.ndarray: 带或不带噪声的TDOA测量值（距离差）。
    """
    # 1. 计算信号从真实位置到达每个观测站的真实飞行时间
    arrival_times = np.linalg.norm(anchors - true_pos, axis=1) / c
    # 2. 计算真实的TDOA（时间差），并转换为距离差
    tdoa_dist_diff = (arrival_times - arrival_times[ref_idx]) * c
    
    # 3. 如果指定了噪声标准差，则添加高斯噪声
    if noise_std > 0:
        # 噪声加在距离差上
        noise = np.random.normal(0, noise_std, size=tdoa_dist_diff.shape)
        tdoa_dist_diff += noise
    
    # 4. 删除参考站对应的元素（其值为0），以匹配求解器的输入格式
    tdoa_without_ref = np.delete(tdoa_dist_diff, ref_idx)
    return tdoa_without_ref

def plot_all_uavs_2d_comparison_with_formation(all_est_trajs, uav_true_positions, formation_labels, output_dir):
    """
    绘制所有无人机的2D轨迹对比图，按阵型分段显示
    """
    formation_names = {0: 'Formation Transition', 1: 'V Formation', 2: 'Line Formation', 3: 'Circle Formation'}
    formation_colors = {0: 'gray', 1: 'red', 2: 'blue', 3: 'green'}
    
    uav_ids = list(all_est_trajs.keys())
    fig, axes = plt.subplots(1, len(uav_ids), figsize=(6*len(uav_ids), 5))
    if len(uav_ids) == 1:
        axes = [axes]
    
    for i, uav_id in enumerate(uav_ids):
        ax = axes[i]
        true_traj = uav_true_positions[uav_id]
        est_traj = np.array(all_est_trajs[uav_id])
        
        # 按阵型分段绘制轨迹
        for label in np.unique(formation_labels):
            if label not in formation_names:
                continue
            mask = formation_labels == label
            if np.sum(mask) == 0:
                continue
                
            true_seg = true_traj[mask]
            est_seg = est_traj[mask]
            
            # 绘制真实轨迹段
            ax.plot(true_seg[:, 0], true_seg[:, 1], 
                   color=formation_colors[label], linewidth=2, 
                   label=f'True-{formation_names[label]}' if i == 0 else "")
            
            # 绘制估计轨迹段
            ax.plot(est_seg[:, 0], est_seg[:, 1], 
                   color=formation_colors[label], linewidth=1, linestyle='--',
                   label=f'Est-{formation_names[label]}' if i == 0 else "")
        
        ax.set_xlabel('X Coordinate (m)')
        ax.set_ylabel('Y Coordinate (m)')
        ax.set_title(f'{uav_id} Trajectory Comparison (Formation-based)')
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        if i == 0:  # 只在第一个子图显示图例
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'all_uavs_2d_comparison_formation.png'), 
                dpi=300, bbox_inches='tight')
    # plt.show()

def save_results_to_csv_with_formation(output_filename, uav_id, true_traj, est_traj, formation_labels, ref_idx, timestamps, solve_times):
    """
    保存结果到CSV文件，包含阵型信息
    """
    # 确保est_traj是NumPy数组
    est_traj = np.array(est_traj)
    
    error_x = np.abs(true_traj[:, 0] - est_traj[:, 0])
    error_y = np.abs(true_traj[:, 1] - est_traj[:, 1])
    error_z = np.abs(true_traj[:, 2] - est_traj[:, 2])
    total_error = np.linalg.norm(true_traj - est_traj, axis=1)
    
    formation_names = {0: 'Formation Transition', 1: 'V Formation', 2: 'Line Formation', 3: 'Circle Formation'}
    
    # 构造DataFrame
    data = {
        'timestamp': timestamps if timestamps is not None else np.arange(len(true_traj)),
        'formation_label': formation_labels,
        'formation_name': [formation_names.get(label, f'Unknown Formation {label}') for label in formation_labels],
        'true_X': true_traj[:, 0], 'true_Y': true_traj[:, 1], 'true_Z': true_traj[:, 2],
        'est_X': est_traj[:, 0], 'est_Y': est_traj[:, 1], 'est_Z': est_traj[:, 2],
        'error_X': error_x, 'error_Y': error_y, 'error_Z': error_z,
        'total_error': total_error,
        'solve_time': solve_times
    }
    df = pd.DataFrame(data)
    
    # 保存到CSV
    df.to_csv(output_filename, index=False)
    
    # 打印统计摘要
    mean_error = np.mean(total_error)
    mean_time = np.mean(solve_times)
    print(f"结果已保存至 {output_filename}")
    print(f"-> {uav_id} 平均3D定位误差: {mean_error:.2f} m")
    print(f"-> {uav_id} 平均单点定位时间: {mean_time*1000:.2f} ms")
    
    # 按阵型分析误差
    formation_stats = analyze_formation_errors(true_traj, est_traj, formation_labels)
    print(f"-> {uav_id} 各阵型定位误差统计:")
    for label, stats in formation_stats.items():
        print(f"   {stats['name']}: 平均误差 {stats['mean_total_error']:.2f}m, "
              f"标准差 {stats['std_total_error']:.2f}m, 数据点 {stats['count']}个")
    
    return formation_stats

# --- 结果分析与可视化 ---
def analyze_error_with_file(true_traj, est_traj, uav_id, file_handle):
    """分析定位误差并同时打印到控制台和写入文件"""
    # 确保est_traj是NumPy数组
    est_traj = np.array(est_traj)
    
    # 计算各轴误差
    error_x = np.abs(true_traj[:, 0] - est_traj[:, 0])
    error_y = np.abs(true_traj[:, 1] - est_traj[:, 1])
    error_z = np.abs(true_traj[:, 2] - est_traj[:, 2])
    total_error = np.linalg.norm(true_traj - est_traj, axis=1)
    

    
    # 准备输出内容
    output_lines = [
        f"\n--- {uav_id} 误差分析 ---",
        f"3D定位误差:  平均: {np.mean(total_error):.2f}m, 中位数: {np.median(total_error):.2f}m, 最大: {np.max(total_error):.2f}m",
        f"X轴误差:        平均: {np.mean(error_x):.2f}m, 中位数: {np.median(error_x):.2f}m, 最大: {np.max(error_x):.2f}m",
        f"Y轴误差:        平均: {np.mean(error_y):.2f}m, 中位数: {np.median(error_y):.2f}m, 最大: {np.max(error_y):.2f}m",
        f"Z轴误差:        平均: {np.mean(error_z):.2f}m, 中位数: {np.median(error_z):.2f}m, 最大: {np.max(error_z):.2f}m"
    ]
    
    # 同时输出到控制台和文件
    for line in output_lines:
        print(line)
        file_handle.write(line + "\n")

def plot_all_uavs_2d_comparison(all_true_trajs, all_est_trajs, uav5_traj, uav6_traj, fixed_anchors, ref_station_idx, constraints):
    """
    绘制一张总览图，展示所有无人机（目标+优化）的二维(XY)平面轨迹、固定基站位置和禁飞区。
    """
    plt.figure(figsize=(14, 12))

    # --- 1. 绘制目标无人机 (UAV 1, 2, 3) 的真实轨迹（虚线）和估计轨迹（散点） ---
    target_colors = plt.cm.autumn(np.linspace(0, 1, len(all_true_trajs)))
    for idx, (uav_id, true_traj) in enumerate(all_true_trajs.items()):
        est_traj = all_est_trajs.get(uav_id)
        color = target_colors[idx]
        plt.plot(true_traj[:, 0], true_traj[:, 1], color=color, linestyle='--', linewidth=1.5, label=f'{uav_id} True Path')
        if est_traj is not None and len(est_traj) > 0:
            # 确保est_traj是NumPy数组
            est_traj = np.array(est_traj)
            plt.scatter(est_traj[:, 0], est_traj[:, 1], color=color, s=10, alpha=0.6, label=f'{uav_id} Estimated Path')
        plt.scatter(true_traj[0, 0], true_traj[0, 1], color=color, marker='o', s=100, edgecolor='black', zorder=5, label=f'{uav_id} Start')

    # --- 2. 绘制优化无人机 (UAV 5, 6) 的轨迹 ---
    plt.plot(uav5_traj[:, 0], uav5_traj[:, 1], color='cyan', marker='.', markersize=3, linestyle='-', label='Optimizer UAV 5 Path')
    plt.plot(uav6_traj[:, 0], uav6_traj[:, 1], color='magenta', marker='.', markersize=3, linestyle='-', label='Optimizer UAV 6 Path')

    # --- 3. 绘制固定地面站 (Stations 1-4) 的位置 ---
    plt.scatter(fixed_anchors[:, 0], fixed_anchors[:, 1], c='black', marker='^', s=150, edgecolor='white', zorder=10, label='Fixed Stations')

    # --- 4. 绘制禁飞区 (XY平面投影) ---
    forbidden_zone_labeled = False
    for zone in constraints.get('forbidden_zones', []):
        if zone['type'] == 'box':
            min_bound, max_bound = zone['bounds']
            width, height = max_bound[0] - min_bound[0], max_bound[1] - min_bound[1]
            label = "Forbidden Zone" if not forbidden_zone_labeled else ""
            rect = plt.Rectangle((min_bound[0], min_bound[1]), width, height, facecolor='red', alpha=0.3, edgecolor='black', linestyle='--', label=label)
            plt.gca().add_patch(rect)
            forbidden_zone_labeled = True

    # --- 5. 设置图表属性 ---
    plt.xlabel('X (m)', fontsize=12)
    plt.ylabel('Y (m)', fontsize=12)
    plt.title(f'Overall 2D Trajectory Comparison (Ref Station: {ref_station_idx+1})', fontsize=14)
    plt.legend(bbox_to_anchor=(1.02, 1), loc='upper left', fontsize=10) # 图例放在图表外部
    plt.grid(True, linestyle=':', alpha=0.6)
    plt.axis('equal') # 保证X, Y轴比例相同
    plt.tight_layout(rect=[0, 0, 0.85, 1]) # 调整布局为图例留出空间
    # plt.show()

def plot_error_vs_time(all_true_trajs, all_est_trajs, timestamps, output_dir):
    """
    绘制所有目标无人机的2D水平定位误差随时间变化的曲线图
    """
    print("\nGenerating horizontal error vs. time plot...")
    plt.figure(figsize=(15, 7))

    colors = plt.cm.viridis(np.linspace(0, 1, len(all_true_trajs)))

    for i, (uav_id, true_traj) in enumerate(all_true_trajs.items()):
        est_traj = np.array(all_est_trajs.get(uav_id, []))
        
        if len(est_traj) > 0 and len(est_traj) == len(true_traj):
            # 仅提取X, Y两轴数据计算水平误差
            true_traj_xy = true_traj[:, :2]
            est_traj_xy = est_traj[:, :2]
            
            # 计算每个时间点的2D水平误差
            errors_2d = np.linalg.norm(true_traj_xy - est_traj_xy, axis=1)
            
            # 绘制误差曲线
            plt.plot(timestamps, errors_2d, label=f'{uav_id} Horizontal Error', 
                    color=colors[i], alpha=0.8, linewidth=1.5)

    plt.xlabel('Time (s)', fontsize=12)
    plt.ylabel('2D (Horizontal) Positioning Error (m)', fontsize=12)
    plt.title('Horizontal Positioning Error vs. Time', fontsize=14)
    plt.legend()
    plt.grid(True, linestyle=':', alpha=0.6)
    plt.tight_layout()
    plt.ylim(bottom=0)

    # 保存图表到文件
    output_path = os.path.join(output_dir, "error_xy_vs_time_plot.png")
    plt.savefig(output_path)
    print(f"Horizontal error plot saved to: {output_path}")
    
    # plt.show()

def create_simulation_animation(all_true_trajs, uav5_traj, uav6_traj, fixed_anchors, output_dir, ref_station_idx, view_angle=None):
    """
    创建并保存一个展示整个动态仿真过程的3D动画，支持从不同视角观看。
    """
    view_name = view_angle if view_angle else '3d'
    print(f"\nCreating '{view_name}' view simulation animation...")
    
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 为加快动画生成速度，可以进行抽帧
    num_frames_total = len(uav5_traj)
    frame_step = 50
    frames_to_render = np.arange(0, num_frames_total, frame_step)

    # --- 1. 初始化静态和动态绘图元素 ---
    ax.scatter(fixed_anchors[:, 0], fixed_anchors[:, 1], fixed_anchors[:, 2], c='black', marker='^', s=100, label='Fixed Stations')
    
    # 动态元素：目标无人机的轨迹线和当前位置点
    target_plots = {}
    target_colors = plt.cm.autumn(np.linspace(0, 1, len(all_true_trajs)))
    for i, (uav_id, true_traj) in enumerate(all_true_trajs.items()):
        line, = ax.plot([], [], [], lw=2, color=target_colors[i], label=f'{uav_id} Path')
        point, = ax.plot([], [], [], 'o', markersize=8, color=target_colors[i])
        target_plots[uav_id] = {'line': line, 'point': point, 'data': true_traj}

    # 移动站点的轨迹线和当前位置点
    uav5_line, = ax.plot([], [], [], lw=2, color='cyan', linestyle='--', alpha=0.7, label='UAV5 Station Path')
    uav5_point, = ax.plot([], [], [], 'D', markersize=10, color='cyan', label='UAV5 Station')
    uav6_line, = ax.plot([], [], [], lw=2, color='magenta', linestyle='--', alpha=0.7, label='UAV6 Station Path')
    uav6_point, = ax.plot([], [], [], 'D', markersize=10, color='magenta', label='UAV6 Station')
    
    # --- 2. 设置图表属性 ---
    ax.set_xlabel('X Coordinate (m)')
    ax.set_ylabel('Y Coordinate (m)')
    ax.set_zlabel('Z Coordinate (m)')
    ax.set_title(f'Dynamic TDOA Positioning Simulation ({view_name.upper()} View)')
    ax.legend()
    
    # 根据视角设置相机角度
    if view_angle == 'top':
        ax.view_init(elev=90, azim=0)
    elif view_angle == 'front':
        ax.view_init(elev=0, azim=0)
    else:  # 默认3D视角
        ax.view_init(elev=20, azim=45)
    
    # --- 3. 定义动画更新函数 ---
    def update(frame):
        # 更新每个目标无人机的轨迹线和当前位置点
        for uav_id, plots in target_plots.items():
            plots['line'].set_data(plots['data'][:frame, 0], plots['data'][:frame, 1])
            plots['line'].set_3d_properties(plots['data'][:frame, 2])
            plots['point'].set_data(plots['data'][frame, 0:1], plots['data'][frame, 1:2])
            plots['point'].set_3d_properties(plots['data'][frame, 2:3])
        
        # 更新移动站点UAV5的轨迹线和位置点
        uav5_line.set_data(uav5_traj[:frame, 0], uav5_traj[:frame, 1])
        uav5_line.set_3d_properties(uav5_traj[:frame, 2])
        uav5_point.set_data(uav5_traj[frame, 0:1], uav5_traj[frame, 1:2])
        uav5_point.set_3d_properties(uav5_traj[frame, 2:3])
        
        # 更新移动站点UAV6的轨迹线和位置点
        uav6_line.set_data(uav6_traj[:frame, 0], uav6_traj[:frame, 1])
        uav6_line.set_3d_properties(uav6_traj[:frame, 2])
        uav6_point.set_data(uav6_traj[frame, 0:1], uav6_traj[frame, 1:2])
        uav6_point.set_3d_properties(uav6_traj[frame, 2:3])
        
        # 返回所有需要重绘的元素
        return (list(p['line'] for p in target_plots.values()) + 
                list(p['point'] for p in target_plots.values()) + 
                [uav5_line, uav5_point, uav6_line, uav6_point])

    # --- 4. 创建并保存动画 ---
    print(f"Rendering {len(frames_to_render)} frames for {view_name} animation...")
    ani = animation.FuncAnimation(fig, update, frames=frames_to_render, interval=50, blit=True, repeat=False)
    
    animation_filename = os.path.join(output_dir, f"simulation_animation_{view_name}.mp4")
    try:
        writer = animation.FFMpegWriter(fps=15, metadata=dict(artist='Simulation'), bitrate=1800)
        ani.save(animation_filename, writer=writer)
        print(f"Animation successfully saved to: {animation_filename}")
    except FileNotFoundError:
        print(f"\n[ERROR] `ffmpeg` not found. Cannot save animation '{animation_filename}'. Please install ffmpeg and ensure it is in the system's PATH.")
    
    plt.close(fig) # 关闭图形，避免在Jupyter Notebook中重复显示

def calculate_tdoa_measurements_vectorized(anchors, true_positions, ref_idx=0, noise_std=0.0):
    """
    矢量化计算多个目标位置的TDOA测量值
    
    参数:
        anchors: 观测站坐标 (N_anchors, 3)
        true_positions: 目标位置数组 (N_targets, 3) 或单个位置 (3,)
        ref_idx: 参考站索引
        noise_std: 噪声标准差
    
    返回:
        TDOA测量值数组
    """
    if true_positions.ndim == 1:
        true_positions = true_positions.reshape(1, -1)
    
    # 计算所有目标到所有基站的距离 (N_targets, N_anchors)
    diffs = true_positions[:, np.newaxis, :] - anchors[np.newaxis, :, :]
    distances = np.linalg.norm(diffs, axis=2)
    
    # 计算到达时间
    arrival_times = distances / c
    
    # 计算TDOA (相对于参考站)
    tdoa_times = arrival_times - arrival_times[:, ref_idx:ref_idx+1]
    tdoa_distances = tdoa_times * c
    
    # 添加噪声
    if noise_std > 0:
        noise = np.random.normal(0, noise_std, tdoa_distances.shape)
        tdoa_distances += noise
    
    # 移除参考站列
    tdoa_without_ref = np.delete(tdoa_distances, ref_idx, axis=1)
    
    return tdoa_without_ref.squeeze() if true_positions.shape[0] == 1 else tdoa_without_ref

def analyze_formation_errors(true_traj, est_traj, formation_labels):
    """
    按阵型分析定位误差
    """
    # 确保输入是NumPy数组
    true_traj = np.array(true_traj)
    est_traj = np.array(est_traj)
    
    formation_stats = {}
    formation_names = {0: 'Formation Transition', 1: 'V Formation', 2: 'Line Formation', 3: 'Circle Formation'}
    
    for label in np.unique(formation_labels):
        if label not in formation_names:
            continue
            
        mask = formation_labels == label
        if np.sum(mask) == 0:
            continue
            
        true_seg = true_traj[mask]
        est_seg = est_traj[mask]
        
        # 计算各轴误差
        error_x = np.abs(true_seg[:, 0] - est_seg[:, 0])
        error_y = np.abs(true_seg[:, 1] - est_seg[:, 1]) 
        error_z = np.abs(true_seg[:, 2] - est_seg[:, 2])
        total_error = np.linalg.norm(true_seg - est_seg, axis=1)
        
        formation_stats[label] = {
            'name': formation_names[label],
            'count': np.sum(mask),
            'mean_error_x': np.mean(error_x),
            'mean_error_y': np.mean(error_y),
            'mean_error_z': np.mean(error_z),
            'mean_total_error': np.mean(total_error),
            'std_total_error': np.std(total_error),
            'max_total_error': np.max(total_error)
        }
    
    return formation_stats

def generate_dynamic_airspace_grid(target_positions, grid_density=5):
    """
    根据目标位置动态生成监视空域网格
    
    参数:
        target_positions (np.ndarray): 目标位置数组，形状为 (n_targets, 3)
        grid_density (int): 网格密度，每个维度的网格点数量
    
    返回:
        np.ndarray: 动态空域网格点坐标 (N_points x 3)
    """
    # 计算目标位置的包围盒
    min_coords = np.min(target_positions, axis=0)
    max_coords = np.max(target_positions, axis=0)
    
    # 添加边距
    margin = 5000  # 5公里边距
    min_coords -= margin
    max_coords += margin
    
    # 生成网格
    x_pts = np.linspace(min_coords[0], max_coords[0], grid_density)
    y_pts = np.linspace(min_coords[1], max_coords[1], grid_density)
    z_pts = np.linspace(min_coords[2], max_coords[2], max(3, grid_density//2))
    
    xv, yv, zv = np.meshgrid(x_pts, y_pts, z_pts, indexing='ij')
    dynamic_airspace_grid = np.vstack([xv.ravel(), yv.ravel(), zv.ravel()]).T
    
    return dynamic_airspace_grid

def generate_tdoa_measurements(anchors, true_pos, ref_idx=0, noise_std=0.0):
    """
    生成TDOA测量值（这是calculate_tdoa_measurements的别名）
    
    参数:
        anchors (np.ndarray): 观测站坐标
        true_pos (np.ndarray): 目标的真实位置
        ref_idx (int): 参考站索引
        noise_std (float): 噪声标准差
    
    返回:
        np.ndarray: TDOA测量值
    """
    return calculate_tdoa_measurements(anchors, true_pos, ref_idx, noise_std)

def plot_trajectories(true_traj, est_traj, uav_id, output_dir):
    """绘制三维轨迹对比图，使用散点图表示，单独绘制每个图，坐标轴单位长度统一一致"""
    # 3D Trajectory Comparison
    fig = plt.figure(figsize=(10, 10))
    ax1 = fig.add_subplot(111, projection='3d')
    ax1.scatter(true_traj[:, 0], true_traj[:, 1], true_traj[:, 2],
                label='True Path', color='blue', s=10, alpha=0.5, zorder=7)
    ax1.scatter(est_traj[:, 0], est_traj[:, 1], est_traj[:, 2],
                label='Estimated Path', color='red', s=10, alpha=0.5, zorder=1)
    
    # 计算坐标轴范围，确保单位长度一致
    all_coords = np.vstack([true_traj, est_traj])
    x_center = np.mean(all_coords[:, 0])
    y_center = np.mean(all_coords[:, 1])
    z_center = np.mean(all_coords[:, 2])
    
    x_range = np.max(all_coords[:, 0]) - np.min(all_coords[:, 0])
    y_range = np.max(all_coords[:, 1]) - np.min(all_coords[:, 1])
    z_range = np.max(all_coords[:, 2]) - np.min(all_coords[:, 2])
    max_range = max(x_range, y_range, z_range) / 2
    
    # 设置坐标轴范围
    ax1.set_xlim(x_center - max_range, x_center + max_range)
    ax1.set_ylim(y_center - max_range, y_center + max_range)
    ax1.set_zlim(z_center - max_range, z_center + max_range)
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title(f'{uav_id} 3D Trajectory Comparison')
    ax1.legend()
    ax1.view_init(elev=25, azim=-60)
    ax1.set_box_aspect([1, 1, 1])
    # 保存图表到文件
    os.makedirs(output_dir, exist_ok=True)  # 确保输出目录存在
    output_path = os.path.join(output_dir, "3D Trajectory Comparison.png")
    plt.savefig(output_path)
    print(f"3D Trajectory Comparison saved to: {output_path}")
    plt.close()  # 关闭图形以释放内存

def run_six_fixed_stations_comparison(all_anchors, uav_true_positions, formation_labels, ref_idx, noise_std):
    """
    运行6固定基站定位算法进行对比分析
    """
    print("\n正在运行6固定基站定位对比分析...")
    six_fixed_anchors = all_anchors  # 使用所有6个基站作为固定基站
    uav_ids = list(uav_true_positions.keys())
    
    six_fixed_results = {}
    
    # 计算总的定位次数用于进度条
    total_positions = sum(len(uav_true_positions[uav_id]) for uav_id in uav_ids)
    
    with tqdm(total=total_positions, desc="6固定基站定位", unit="点") as pbar:
        for uav_id in uav_ids:
            true_traj = uav_true_positions[uav_id]
            est_traj = []
            solve_times = []
            prev_est = None
            
            for true_pos in true_traj:
                # 使用Chan+LM算法
                start_time = time.perf_counter()
                tdoa_meas = calculate_tdoa_measurements(six_fixed_anchors, true_pos, ref_idx, noise_std)

                if prev_est is None:
                    initial_guess = chan_initial(six_fixed_anchors, tdoa_meas, ref_idx)
                else:
                    initial_guess = prev_est

                pos, _ = newton_tdoa_solver_lm(six_fixed_anchors, tdoa_meas, initial_guess, ref_idx)
                solve_time = time.perf_counter() - start_time
                
                prev_est = pos
                est_traj.append(pos)
                solve_times.append(solve_time)
                
                pbar.update(1)
            
            est_traj = np.array(est_traj)
            total_error = np.linalg.norm(true_traj - est_traj, axis=1)
            formation_stats = analyze_formation_errors(true_traj, est_traj, formation_labels)
            
            six_fixed_results[uav_id] = {
                'est_traj': est_traj,
                'solve_times': solve_times,
                'mean_error': np.mean(total_error),
                'mean_time': np.mean(solve_times),
                'formation_stats': formation_stats,
                'total_error': total_error
            }
    
    print("6固定基站定位对比分析完成。")
    return six_fixed_results

def run_algorithm_comparison_with_dynamic_anchors(dynamic_anchors_per_frame, uav_true_positions, ref_idx, noise_std):
    """
    对比 Chan-only / Newton-only / Chan+Newton 三种算法
    使用每一帧的动态6基站 (4固定+2移动) 布局
    """
    print("\n正在运行【4+2配置】的算法对比分析...")
    import time
    from tqdm import tqdm
    import numpy as np

    algorithms = ['Chan-only', 'Newton-only', 'Chan+Newton']
    uav_ids = list(uav_true_positions.keys())
    algorithm_results = {alg: {} for alg in algorithms}

    total_positions = len(algorithms) * sum(len(uav_true_positions[u]) for u in uav_ids)

    with tqdm(total=total_positions, desc="算法对比（4+2配置）", unit="点") as pbar:
        for algorithm in algorithms:
            for uav_id in uav_ids:
                true_traj = uav_true_positions[uav_id]
                n_points = len(true_traj)
                est_traj = np.zeros_like(true_traj)
                solve_times = np.zeros(n_points)

                prev_est = None
                for i, true_pos in enumerate(true_traj):
                    anchors = dynamic_anchors_per_frame[i]
                    tdoa_meas = generate_tdoa_measurements(anchors, true_pos, ref_idx, noise_std)

                    start_time = time.perf_counter()
                    try:
                        if algorithm == 'Chan-only':
                            pos = chan_initial(anchors, tdoa_meas, ref_idx)
                        elif algorithm == 'Newton-only':
                            initial_guess = prev_est if prev_est is not None else np.mean(anchors, axis=0)
                            pos = newton_tdoa_solver(anchors, tdoa_meas, initial_guess, ref_idx)
                        elif algorithm == 'Chan+Newton':
                            initial_guess = prev_est if prev_est is not None else chan_initial(anchors, tdoa_meas, ref_idx)
                            pos, _ = newton_tdoa_solver_lm(anchors, tdoa_meas, initial_guess, ref_idx)
                        if pos is None or np.any(np.isnan(pos)) or np.any(np.isinf(pos)):
                            raise ValueError("无效解")
                    except Exception as e:
                        print(f"[警告] {algorithm}在{uav_id}第{i}帧出错: {e}")
                        pos = prev_est if prev_est is not None else np.mean(anchors, axis=0)

                    solve_times[i] = time.perf_counter() - start_time
                    est_traj[i] = pos
                    prev_est = pos
                    pbar.update(1)

                error = np.linalg.norm(true_traj - est_traj, axis=1)
                error_x = np.abs(true_traj[:, 0] - est_traj[:, 0])
                error_y = np.abs(true_traj[:, 1] - est_traj[:, 1])
                error_z = np.abs(true_traj[:, 2] - est_traj[:, 2])

                algorithm_results[algorithm][uav_id] = {
                    'true_traj': true_traj,
                    'est_traj': est_traj,
                    'solve_times': solve_times,
                    'mean_time': np.mean(solve_times),
                    'total_time': np.sum(solve_times),
                    'total_error': error,
                    'mean_error': np.mean(error),
                    'error_x': error_x,
                    'error_y': error_y,
                    'error_z': error_z
                }

    print("算法对比分析（4+2配置）完成。")
    return algorithm_results

def prepare_dynamic_anchors(fixed_anchors, uav5_trajectory_history, uav6_trajectory_history):
    import numpy as np
    dynamic_anchors_per_frame = []
    uav5 = np.array(uav5_trajectory_history)
    uav6 = np.array(uav6_trajectory_history)
    for i in range(len(uav5)):
        dynamic_anchors = np.vstack((fixed_anchors, uav5[i], uav6[i]))
        dynamic_anchors_per_frame.append(dynamic_anchors)
    return dynamic_anchors_per_frame

def run_algorithm_comparison(all_anchors, uav_true_positions, ref_idx, noise_std):
    """
    运行三种算法对比分析：Chan-only, Newton-only, Chan+Newton
    返回每种方法在每个 UAV 上的估计轨迹、误差、时间等统计信息
    """
    print("\n正在运行算法对比分析...")
    import time
    from tqdm import tqdm
    import numpy as np

    algorithms = ['Chan-only', 'Newton-only', 'Chan+Newton']
    uav_ids = list(uav_true_positions.keys())
    algorithm_results = {alg: {} for alg in algorithms}

    total_positions = len(algorithms) * sum(len(uav_true_positions[u]) for u in uav_ids)

    with tqdm(total=total_positions, desc="算法对比分析", unit="点") as pbar:
        for algorithm in algorithms:
            for uav_id in uav_ids:
                true_traj = uav_true_positions[uav_id]
                n_points = len(true_traj)
                est_traj = np.zeros_like(true_traj)
                solve_times = np.zeros(n_points)
                tdoa_measurements = calculate_tdoa_measurements_vectorized(
                    all_anchors, true_traj, ref_idx, noise_std)

                prev_est = None
                for i, (true_pos, tdoa_meas) in enumerate(zip(true_traj, tdoa_measurements)):
                    start_time = time.perf_counter()
                    try:
                        if algorithm == 'Chan-only':
                            pos = chan_initial(all_anchors, tdoa_meas, ref_idx)

                        elif algorithm == 'Newton-only':
                            initial_guess = prev_est if prev_est is not None else np.mean(all_anchors, axis=0)
                            pos = newton_tdoa_solver(all_anchors, tdoa_meas, initial_guess, ref_idx)

                        elif algorithm == 'Chan+Newton':
                            initial_guess = prev_est if prev_est is not None else chan_initial(all_anchors, tdoa_meas, ref_idx)
                            pos, _ = newton_tdoa_solver_lm(all_anchors, tdoa_meas, initial_guess, ref_idx)

                        if pos is None or np.any(np.isnan(pos)) or np.any(np.isinf(pos)):
                            raise ValueError("解无效")

                    except Exception as e:
                        print(f"警告: {algorithm} 算法在 {uav_id} 第 {i} 点出错: {e}")
                        pos = prev_est if prev_est is not None else np.mean(all_anchors, axis=0)

                    solve_times[i] = time.perf_counter() - start_time
                    est_traj[i] = pos
                    prev_est = pos
                    pbar.update(1)

                error = np.linalg.norm(true_traj - est_traj, axis=1)
                error_x = np.abs(true_traj[:, 0] - est_traj[:, 0])
                error_y = np.abs(true_traj[:, 1] - est_traj[:, 1])
                error_z = np.abs(true_traj[:, 2] - est_traj[:, 2])

                algorithm_results[algorithm][uav_id] = {
                    'true_traj': true_traj,
                    'est_traj': est_traj,
                    'solve_times': solve_times,
                    'mean_time': np.mean(solve_times),
                    'total_time': np.sum(solve_times),
                    'total_error': error,
                    'mean_error': np.mean(error),
                    'error_x': error_x,
                    'error_y': error_y,
                    'error_z': error_z
                }

    print("算法对比分析完成。")
    return algorithm_results


def write_comparison_analysis_to_file(file_handle, mobile_results, six_fixed_results, algorithm_results, formation_labels, uav_ids):
    """将对比分析结果写入文件"""
    
    # 3. 6固定 vs 4固定+2移动对比
    file_handle.write("3. 基站配置对比分析 (6固定 vs 4固定+2移动)\n")
    file_handle.write("="*80 + "\n")
    
    # 计算总体误差统计
    mobile_all_errors = []
    six_fixed_all_errors = []
    
    for uav_id in uav_ids:
        mobile_all_errors.extend(mobile_results[uav_id]['total_error'])
        six_fixed_all_errors.extend(six_fixed_results[uav_id]['total_error'])
    
    mobile_all_errors = np.array(mobile_all_errors)
    six_fixed_all_errors = np.array(six_fixed_all_errors)
    
    file_handle.write("基站配置总体性能对比:\n")
    file_handle.write("-" * 80 + "\n")
    file_handle.write(f"{'配置':<20} {'平均误差(m)':<15} {'误差标准差(m)':<15} {'最大误差(m)':<15} {'中位数误差(m)':<15}\n")
    file_handle.write("-" * 80 + "\n")
    
    configs = [
        ("4固定+2移动", mobile_all_errors),
        ("6固定基站", six_fixed_all_errors)
    ]
    
    for config_name, errors in configs:
        mean_error = np.mean(errors)
        std_error = np.std(errors)
        max_error = np.max(errors)
        median_error = np.median(errors)
        file_handle.write(f"{config_name:<20} {mean_error:<15.2f} {std_error:<15.2f} {max_error:<15.2f} {median_error:<15.2f}\n")
    
    # 4. 算法对比分析
    file_handle.write(f"\n\n4. 算法性能对比分析\n")
    file_handle.write("="*80 + "\n")
    
    # 4.1 总体3D误差对比
    file_handle.write(f"\n4.1 三种算法3D总误差对比:\n")
    file_handle.write("-" * 80 + "\n")
    file_handle.write(f"{'算法':<15} {'平均误差(m)':<15} {'误差标准差(m)':<15} {'最大误差(m)':<15} {'中位数误差(m)':<15}\n")
    file_handle.write("-" * 80 + "\n")
    
    for algorithm in ['Chan-only', 'Newton-only', 'Chan+Newton']:
        all_errors = []
        for uav_id in uav_ids:
            all_errors.extend(algorithm_results[algorithm][uav_id]['total_error'])
        
        all_errors = np.array(all_errors)
        mean_error = np.mean(all_errors)
        std_error = np.std(all_errors)
        max_error = np.max(all_errors)
        median_error = np.median(all_errors)
        
        file_handle.write(f"{algorithm:<15} {mean_error:<15.2f} {std_error:<15.2f} {max_error:<15.2f} {median_error:<15.2f}\n")
    
    # 4.2 XY两轴误差单独对比
    file_handle.write(f"\n4.2 三种算法XYZ三轴误差对比:\n")
    file_handle.write("-" * 80 + "\n")
    
    # X轴误差对比
    file_handle.write("X轴误差对比:\n")
    file_handle.write(f"{'算法':<15} {'平均误差(m)':<15} {'误差标准差(m)':<15} {'最大误差(m)':<15} {'中位数误差(m)':<15}\n")
    file_handle.write("-" * 50 + "\n")
    
    for algorithm in ['Chan-only', 'Newton-only', 'Chan+Newton']:
        all_x_errors = []
        for uav_id in uav_ids:
            all_x_errors.extend(algorithm_results[algorithm][uav_id]['error_x'])
        
        all_x_errors = np.array(all_x_errors)
        mean_error = np.mean(all_x_errors)
        std_error = np.std(all_x_errors)
        max_error = np.max(all_x_errors)
        median_error = np.median(all_x_errors)
        
        file_handle.write(f"{algorithm:<15} {mean_error:<15.2f} {std_error:<15.2f} {max_error:<15.2f} {median_error:<15.2f}\n")
    
    # Y轴误差对比
    file_handle.write("\nY轴误差对比:\n")
    file_handle.write(f"{'算法':<15} {'平均误差(m)':<15} {'误差标准差(m)':<15} {'最大误差(m)':<15} {'中位数误差(m)':<15}\n")
    file_handle.write("-" * 50 + "\n")
    
    for algorithm in ['Chan-only', 'Newton-only', 'Chan+Newton']:
        all_y_errors = []
        for uav_id in uav_ids:
            all_y_errors.extend(algorithm_results[algorithm][uav_id]['error_y'])
        
        all_y_errors = np.array(all_y_errors)
        mean_error = np.mean(all_y_errors)
        std_error = np.std(all_y_errors)
        max_error = np.max(all_y_errors)
        median_error = np.median(all_y_errors)
        
        file_handle.write(f"{algorithm:<15} {mean_error:<15.2f} {std_error:<15.2f} {max_error:<15.2f} {median_error:<15.2f}\n")
    
    # Z轴误差对比
    file_handle.write("\nZ轴误差对比:\n")
    file_handle.write(f"{'算法':<15} {'平均误差(m)':<15} {'误差标准差(m)':<15} {'最大误差(m)':<15} {'中位数误差(m)':<15}\n")
    file_handle.write("-" * 50 + "\n")
    
    for algorithm in ['Chan-only', 'Newton-only', 'Chan+Newton']:
        all_z_errors = []
        for uav_id in uav_ids:
            all_z_errors.extend(algorithm_results[algorithm][uav_id]['error_z'])
        
        all_z_errors = np.array(all_z_errors)
        mean_error = np.mean(all_z_errors)
        std_error = np.std(all_z_errors)
        max_error = np.max(all_z_errors)
        median_error = np.median(all_z_errors)
        
        file_handle.write(f"{algorithm:<15} {mean_error:<15.2f} {std_error:<15.2f} {max_error:<15.2f} {median_error:<15.2f}\n")
    
    # 4.3 各UAV算法性能详细对比
    file_handle.write(f"\n4.3 各UAV算法性能详细对比:\n")
    file_handle.write("-" * 80 + "\n")
    
    for uav_id in uav_ids:
        file_handle.write(f"\n{uav_id} 算法性能对比:\n")
        file_handle.write(f"{'算法':<15} {'平均3D误差(m)':<18} {'平均时间(ms)':<15}\n")
        file_handle.write("-" * 48 + "\n")
        
        for algorithm in ['Chan-only', 'Newton-only', 'Chan+Newton']:
            mean_error = algorithm_results[algorithm][uav_id]['mean_error']
            mean_time = algorithm_results[algorithm][uav_id]['mean_time']
            file_handle.write(f"{algorithm:<15} {mean_error:<18.2f} {mean_time*1000:<15.2f}\n")
        
        # 添加XYZ轴误差详细对比
        file_handle.write(f"\n{uav_id} XYZ轴误差详细对比:\n")
        file_handle.write(f"{'算法':<15} {'X轴平均(m)':<12} {'Y轴平均(m)':<12} {'Z轴平均(m)':<12}\n")
        file_handle.write("-" * 51 + "\n")
        
        for algorithm in ['Chan-only', 'Newton-only', 'Chan+Newton']:
            mean_x = np.mean(algorithm_results[algorithm][uav_id]['error_x'])
            mean_y = np.mean(algorithm_results[algorithm][uav_id]['error_y'])
            mean_z = np.mean(algorithm_results[algorithm][uav_id]['error_z'])
            file_handle.write(f"{algorithm:<15} {mean_x:<12.2f} {mean_y:<12.2f} {mean_z:<12.2f}\n")

def save_results_with_file_output(output_filename, uav_id, true_traj, est_traj, formation_labels, ref_idx, timestamps, solve_times, file_handle):
    """保存结果并同时输出到文件"""
    # 确保est_traj是NumPy数组
    est_traj = np.array(est_traj)
    
    formation_stats = save_results_to_csv_with_formation(output_filename, uav_id, true_traj, est_traj, formation_labels, ref_idx, timestamps, solve_times)
    
    # 将阵型统计写入文件
    mean_error = np.mean(np.linalg.norm(true_traj - est_traj, axis=1))
    mean_time = np.mean(solve_times)
    
    file_handle.write(f"\n结果已保存至 {output_filename}\n")
    file_handle.write(f"-> {uav_id} 平均3D定位误差: {mean_error:.2f} m\n")
    file_handle.write(f"-> {uav_id} 平均单点定位时间: {mean_time*1000:.2f} ms\n")
    file_handle.write(f"-> {uav_id} 各阵型定位误差统计:\n")
    
    for label, stats in formation_stats.items():
        file_handle.write(f"   {stats['name']}: 平均误差 {stats['mean_total_error']:.2f}m, "
                         f"标准差 {stats['std_total_error']:.2f}m, 数据点 {stats['count']}个\n")
    
    return formation_stats

def plot_xyz_error_comparison(algorithm_results, uav_ids, output_dir):
    """
    绘制三种算法的XYZ三轴误差对比图
    """
    print("\n正在生成XYZ三轴误差对比图...")
    
    algorithms = ['Chan-only', 'Newton-only', 'Chan+Newton']
    axes_names = ['X轴误差', 'Y轴误差', 'Z轴误差']
    error_keys = ['error_x', 'error_y', 'error_z']
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    for axis_idx, (axis_name, error_key) in enumerate(zip(axes_names, error_keys)):
        ax = axes[axis_idx]
        
        # 为每个算法收集所有UAV的误差数据
        algorithm_errors = []
        algorithm_labels = []
        
        for algorithm in algorithms:
            all_errors = []
            for uav_id in uav_ids:
                all_errors.extend(algorithm_results[algorithm][uav_id][error_key])
            algorithm_errors.append(all_errors)
            algorithm_labels.append(algorithm)
        
        # 绘制箱线图
        bp = ax.boxplot(algorithm_errors, tick_labels=algorithm_labels, patch_artist=True)
        
        # 设置颜色
        colors = ['lightblue', 'lightgreen', 'lightcoral']
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
        
        ax.set_ylabel('误差 (m)')
        ax.set_title(f'{axis_name}对比')
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'xyz_error_comparison.png'), dpi=300, bbox_inches='tight')
    # plt.show()
    print(f"XYZ三轴误差对比图已保存至: {output_dir}/xyz_error_comparison.png")

def create_3d_trajectory_comparison_plot(output_dir, uav_true_positions, all_est_trajs, formation_labels):
    """
    创建3D轨迹对比图，不同无人机用不同形状，不同阵型用不同颜色
    """
    print("\n正在生成3D轨迹对比图...")
    
    fig = plt.figure(figsize=(15, 12))
    ax = fig.add_subplot(111, projection='3d')
    
    # 定义阵型颜色和名称
    formation_colors = {0: 'gray', 1: 'red', 2: 'blue', 3: 'green'}
    formation_names = {0: 'Transition', 1: 'V Formation', 2: 'Line Formation', 3: 'Circle Formation'}
    
    # 定义无人机形状
    uav_markers = {'UAV1': 'o', 'UAV2': 's', 'UAV3': '^'}
    uav_ids = list(uav_true_positions.keys())
    
    # 为每个阵型和UAV组合绘制轨迹段
    for uav_id in uav_ids:
        true_traj = uav_true_positions[uav_id]
        est_traj = np.array(all_est_trajs[uav_id])
        
        for formation_label in np.unique(formation_labels):
            if formation_label not in formation_colors:
                continue
                
            mask = formation_labels == formation_label
            if np.sum(mask) == 0:
                continue
            
            true_seg = true_traj[mask]
            est_seg = est_traj[mask]
            
            color = formation_colors[formation_label]
            marker = uav_markers.get(uav_id, 'o')
            
            # 绘制真实轨迹段
            ax.scatter(true_seg[:, 0], true_seg[:, 1], true_seg[:, 2],
                      c=color, marker=marker, s=20, alpha=0.8,
                      label=f'{uav_id} True - {formation_names[formation_label]}' if formation_label == 1 else "")
            
            # 绘制估计轨迹段
            ax.scatter(est_seg[:, 0], est_seg[:, 1], est_seg[:, 2],
                      c=color, marker=marker, s=10, alpha=0.4, edgecolors='black', linewidth=0.5,
                      label=f'{uav_id} Est - {formation_names[formation_label]}' if formation_label == 1 else "")
    
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_title('3D Trajectory Comparison: Formation-based Coloring')
    
    # 创建自定义图例
    legend_elements = []
    
    # 添加阵型颜色图例
    for label, color in formation_colors.items():
        if label in formation_names:
            legend_elements.append(plt.Line2D([0], [0], marker='o', color='w', 
                                            markerfacecolor=color, markersize=8,
                                            label=formation_names[label]))
    
    # 添加UAV形状图例
    for uav_id, marker in uav_markers.items():
        legend_elements.append(plt.Line2D([0], [0], marker=marker, color='black', 
                                        linestyle='None', markersize=8,
                                        label=f'{uav_id} Shape'))
    
    # 添加真实vs估计图例
    legend_elements.append(plt.Line2D([0], [0], marker='o', color='black', 
                                    alpha=0.8, linestyle='None', markersize=8,
                                    label='True Trajectory'))
    legend_elements.append(plt.Line2D([0], [0], marker='o', color='black', 
                                    alpha=0.4, linestyle='None', markersize=6,
                                    markeredgecolor='black', markeredgewidth=0.5,
                                    label='Estimated Trajectory'))
    
    ax.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1), loc='upper left')
    ax.view_init(elev=25, azim=-60)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, '3d_trajectory_comparison.png'), dpi=300, bbox_inches='tight')
    plt.show()
    print(f"3D轨迹对比图已保存至: {output_dir}/3d_trajectory_comparison.png")

def create_trajectory_video(output_dir, uav_true_positions, all_est_trajs, timestamps, formation_labels, all_anchors):
    """创建3D轨迹对比动画视频"""
    try:
        import matplotlib.pyplot as plt
        import matplotlib.animation as animation
        from mpl_toolkits.mplot3d import Axes3D
        
        print("\n正在生成3D轨迹对比动画视频...")
        
        fig = plt.figure(figsize=(15, 12))
        ax = fig.add_subplot(111, projection='3d')
        
        uav_ids = list(uav_true_positions.keys())
        uav_markers = {'UAV1': 'o', 'UAV2': 's', 'UAV3': '^'}
        formation_colors = {0: 'gray', 1: 'red', 2: 'blue', 3: 'green'}
        formation_names = {0: 'Transition', 1: 'V Formation', 2: 'Line Formation', 3: 'Circle Formation'}
        
        # 设置坐标轴范围
        all_positions = np.vstack([uav_true_positions[uav_id] for uav_id in uav_ids])
        x_range = [all_positions[:, 0].min() - 1000, all_positions[:, 0].max() + 1000]
        y_range = [all_positions[:, 1].min() - 1000, all_positions[:, 1].max() + 1000]
        z_range = [all_positions[:, 2].min() - 500, all_positions[:, 2].max() + 500]
        
        # 创建动画帧
        frames = min(len(timestamps), 300)
        step = max(1, len(timestamps) // frames)
        frame_indices = range(0, len(timestamps), step)
        
        def animate(frame_idx):
            frame = frame_indices[frame_idx]
            ax.clear()
            
            # 绘制基站
            fixed_anchors = all_anchors[:4]
            ax.scatter(fixed_anchors[:, 0], fixed_anchors[:, 1], fixed_anchors[:, 2], 
                      c='black', s=100, marker='^', label='Fixed Stations')
            
            # 绘制UAV轨迹历史
            for uav_id in uav_ids:
                true_traj = uav_true_positions[uav_id]
                est_traj = np.array(all_est_trajs[uav_id])
                marker = uav_markers.get(uav_id, 'o')
                
                if frame > 10:  # 显示历史轨迹
                    start_idx = max(0, frame - 50)
                    
                    # 按阵型分段绘制历史轨迹
                    for i in range(start_idx, frame):
                        if i < len(formation_labels):
                            color = formation_colors.get(formation_labels[i], 'gray')
                            
                            # 真实轨迹点
                            ax.scatter([true_traj[i, 0]], [true_traj[i, 1]], [true_traj[i, 2]], 
                                     c=color, marker=marker, s=15, alpha=0.6)
                            
                            # 估计轨迹点
                            ax.scatter([est_traj[i, 0]], [est_traj[i, 1]], [est_traj[i, 2]], 
                                     c=color, marker=marker, s=8, alpha=0.3, 
                                     edgecolors='black', linewidth=0.3)
                
                # 绘制当前位置
                if frame < len(true_traj) and frame < len(formation_labels):
                    current_color = formation_colors.get(formation_labels[frame], 'gray')
                    
                    ax.scatter([true_traj[frame, 0]], [true_traj[frame, 1]], [true_traj[frame, 2]], 
                              c=current_color, marker=marker, s=100, alpha=1.0, 
                              edgecolors='white', linewidth=2,
                              label=f'{uav_id} True')
                    
                    ax.scatter([est_traj[frame, 0]], [est_traj[frame, 1]], [est_traj[frame, 2]], 
                              c=current_color, marker=marker, s=60, alpha=0.7, 
                              edgecolors='black', linewidth=1,
                              label=f'{uav_id} Est')
            
            ax.set_xlim(x_range)
            ax.set_ylim(y_range)
            ax.set_zlim(z_range)
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')
            ax.set_zlabel('Z (m)')
            
            # 显示当前阵型
            current_formation = formation_names.get(formation_labels[frame] if frame < len(formation_labels) else 0, 'Unknown')
            ax.set_title(f'3D Trajectory Animation - Time: {timestamps[frame]:.1f}s - {current_formation}')
            
            ax.legend(loc='upper left', bbox_to_anchor=(0, 1))
            ax.view_init(elev=25, azim=-60)
        
        # 生成动画
        with tqdm(total=len(frame_indices), desc="渲染视频帧", unit="帧") as pbar:
            def animate_with_progress(frame_idx):
                animate(frame_idx)
                pbar.update(1)
                return []
            
            anim = animation.FuncAnimation(fig, animate_with_progress, frames=len(frame_indices), 
                                         interval=200, blit=False)
            
            video_path = os.path.join(output_dir, '3d_trajectory_animation.mp4')
            anim.save(video_path, writer='ffmpeg', fps=5, bitrate=1800)
            plt.close()
        
        print(f"3D轨迹动画视频已保存至: {video_path}")
        
    except Exception as e:
        print(f"视频生成失败: {e}")
        print("请确保已安装ffmpeg")

def create_visualization_plots(output_dir, uav_true_positions, all_est_trajs, formation_labels, timestamps, uav5_trajectory_history, uav6_trajectory_history, all_anchors):
    """创建可视化图表"""
    import matplotlib.pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D
    
    print("\n正在生成可视化图表...")
    
    plots_to_create = [
        "3D轨迹对比图",
        "误差时间序列图", 
        "阵型误差箱线图"
    ]
    
    with tqdm(total=len(plots_to_create), desc="生成图表", unit="图") as pbar:
        # 1. 使用新的3D轨迹对比图
        pbar.set_description("生成3D轨迹对比图")
        create_3d_trajectory_comparison_plot(output_dir, uav_true_positions, all_est_trajs, formation_labels)
        pbar.update(1)
        
        # 2. 误差时间序列图
        pbar.set_description("生成误差时间序列图")
        # ... 保持原有代码 ...
        pbar.update(1)
        
        # 3. 阵型误差箱线图  
        pbar.set_description("生成阵型误差箱线图")
        # ... 保持原有代码 ...
        pbar.update(1)
    
    print(f"可视化图表已保存至: {output_dir}")
    
def print_experiment_parameters_to_file(file_handle, ref_idx, noise_std, opt_period, de_params_dict, constraints_dict, data_filename, uav_ids, num_steps, fixed_anchors, mobile_anchors, solver_algorithm='newton'):
    """
    将实验参数详细信息写入文件和控制台
    """
    # 控制台输出
    print(f"\n{'='*60}")
    print(f"{'实验参数总结':^60}")
    print(f"{'='*60}")
    print(f"{'数据文件:':<30} {data_filename}")
    print(f"{'参考站索引:':<30} {ref_idx} (基站 #{ref_idx + 1})")
    print(f"{'TDOA噪声标准差:':<30} {noise_std} m")
    print(f"{'TDOA求解算法:':<30} {solver_algorithm}")
    print(f"{'优化周期:':<30} 每 {opt_period} 个时间步")
    print(f"{'处理UAV数量:':<30} {len(uav_ids)}")
    print(f"{'总时间步数:':<30} {num_steps}")
    print("-" * 60)
    print("DE优化算法参数:")
    print(f"{'种群大小 (NP):':<30} {de_params_dict['NP']}")
    print(f"{'最大迭代数 (G_max):':<30} {de_params_dict['G_max']}")
    print(f"{'变异因子 (F0):':<30} {de_params_dict['F0']}")
    print("-" * 60)
    print("物理约束:")
    print(f"{'搜索空间 X:':<30} {constraints_dict['search_space']['x']} m")
    print(f"{'搜索空间 Y:':<30} {constraints_dict['search_space']['y']} m")
    print(f"{'搜索空间 Z:':<30} {constraints_dict['search_space']['z']} m")
    print(f"{'最大飞行速度:':<30} {constraints_dict['Vmax']} m/s")
    print(f"{'='*60}")
    print(f"\n当前TDOA求解器配置:")
    print(f"  - 算法: {solver_algorithm}")
    print(f"  - 全局参数: {TDOA_SOLVER_PARAMS}")
    print(f"  - 噪声标准差: {noise_std}m")
    print(f"  - 参考站: 基站#{ref_idx + 1}")
    print(f"{'='*60}")

def plot_mobile_anchors_trajectory(uav5_history, uav6_history, output_dir):
    uav5_history = np.array(uav5_history)
    uav6_history = np.array(uav6_history)

    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    ax.plot(uav5_history[:, 0], uav5_history[:, 1], uav5_history[:, 2], label='UAV5 (Mobile Anchor)', color='blue')
    ax.plot(uav6_history[:, 0], uav6_history[:, 1], uav6_history[:, 2], label='UAV6 (Mobile Anchor)', color='green')

    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_title('移动基站轨迹（UAV5 & UAV6）')
    ax.legend()
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "mobile_anchor_trajectory.png"))
    plt.close()

def plot_solver_time_comparison(algorithm_results, output_dir):
    """
    生成 Chan-only、Newton-only、Chan+LM 三种算法的平均运行时间对比图（单位：毫秒）
    """
    import matplotlib.pyplot as plt
    import numpy as np
    import os

    print("正在生成三种算法平均运行时间对比图...")

    # 指定要绘制的算法顺序和名称
    methods = ['Chan-only', 'Newton-only', 'Chan+Newton']
    mean_times = []

    for m in methods:
        if m not in algorithm_results:
            print(f"警告：算法 {m} 不在结果中，跳过。")
            mean_times.append(0.0)
            continue

        uav_results = algorithm_results[m]
        times = []

        for uav_id in uav_results:
            if 'mean_time' in uav_results[uav_id]:
                times.append(uav_results[uav_id]['mean_time'])

        avg_time_ms = np.mean(times) * 1000 if times else 0.0
        mean_times.append(avg_time_ms)

    # 绘图
    plt.figure(figsize=(8, 6))
    colors = ['#4CAF50', '#2196F3', '#FF9800']
    bars = plt.bar(methods, mean_times, color=colors)

    plt.ylabel("平均运行时间 (ms)")
    plt.title("三种TDOA解算方法运行时间对比")

    for bar, time in zip(bars, mean_times):
        plt.text(bar.get_x() + bar.get_width() / 2, bar.get_height(), f"{time:.1f} ms",
                 ha='center', va='bottom', fontsize=10)

    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.tight_layout()

    os.makedirs(output_dir, exist_ok=True)
    plt.savefig(os.path.join(output_dir, "solver_time_comparison.png"))
    plt.close()
    print("运行时间对比图已保存为 solver_time_comparison.png")

def plot_error_comparison_between_station_layouts(mobile_results, six_fixed_results, output_dir):
    import matplotlib.pyplot as plt

    uav_ids = list(mobile_results.keys())
    mobile_errors = [np.mean(mobile_results[uav]['total_error']) for uav in uav_ids]
    fixed_errors = [np.mean(six_fixed_results[uav]['total_error']) for uav in uav_ids]

    x = np.arange(len(uav_ids))
    width = 0.35

    fig, ax = plt.subplots(figsize=(8, 6))
    rects1 = ax.bar(x - width/2, mobile_errors, width, label='4固定+2移动')
    rects2 = ax.bar(x + width/2, fixed_errors, width, label='6固定')

    ax.set_ylabel('平均误差 (m)')
    ax.set_title('两种基站部署方式误差对比')
    ax.set_xticks(x)
    ax.set_xticklabels(uav_ids)
    ax.legend()

    for rect in rects1 + rects2:
        height = rect.get_height()
        ax.text(rect.get_x() + rect.get_width()/2., height, f'{height:.2f}', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "error_comparison_station_layouts.png"))
    plt.close()

def plot_2d_error_comparison(algorithm_results, uav_ids, save_path=None):
    """
    比较三种算法在XY平面上的二维误差，绘图展示
    """
    algorithms = ['Chan-only', 'Newton-only', 'Chan+Newton']
    mean_errors = []
    std_errors = []

    for algorithm in algorithms:
        all_x = []
        all_y = []
        for uav_id in uav_ids:
            all_x.extend(algorithm_results[algorithm][uav_id]['error_x'])
            all_y.extend(algorithm_results[algorithm][uav_id]['error_y'])
        all_x = np.array(all_x)
        all_y = np.array(all_y)
        error_2d = np.sqrt(all_x**2 + all_y**2)

        mean_errors.append(np.mean(error_2d))
        std_errors.append(np.std(error_2d))

    # 绘图
    fig, ax = plt.subplots()
    bar_width = 0.4
    bars = ax.bar(algorithms, mean_errors, bar_width, yerr=std_errors, capsize=5)

    ax.set_ylabel('二维平均误差 (m)')
    ax.set_title('三种算法XY二维误差对比')
    ax.grid(True, linestyle='--', alpha=0.6)
    ax.set_ylim(0, max(np.array(mean_errors) + np.array(std_errors)) * 1.2)

    # 数值标注
    for bar in bars:
        height = bar.get_height()
        ax.annotate(f'{height:.2f}', xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 5), textcoords="offset points",
                    ha='center', va='bottom')

    plt.tight_layout()
    if save_path:
        plt.savefig(save_path, dpi=300)
        print(f"[图已保存]: {save_path}")
    else:
        plt.show()

# def plot_solver_error_comparison(algorithm_results, output_dir):
#     import matplotlib.pyplot as plt
#     import numpy as np

#     methods = list(algorithm_results.keys())

#     # 计算每个方法的平均误差（如果没有 total_error 字段）
#     mean_errors = [
#         np.mean(np.linalg.norm(
#             algorithm_results[m]['est_traj'] - algorithm_results[m]['true_traj'], axis=1
#         ))
#         for m in methods
#     ]

#     plt.figure(figsize=(8, 6))
#     bars = plt.bar(methods, mean_errors, color=['#4CAF50', '#2196F3', '#FF9800', '#9C27B0'])
#     plt.ylabel("平均误差 (m)")
#     plt.title("不同TDOA解算方法误差对比")

#     for bar, err in zip(bars, mean_errors):
#         plt.text(bar.get_x() + bar.get_width()/2, bar.get_height(), f"{err:.2f} m", ha='center', va='bottom')

#     plt.tight_layout()
#     plt.savefig(os.path.join(output_dir, "solver_error_comparison.png"))
#     plt.close()

# def run_algorithm_comparison_for_plot(all_anchors, uav_true_positions, ref_idx, noise_std):
#     """
#     为画图专用：仅对第一个 UAV 执行三种算法，并返回标准化结构。
#     返回结构：
#     {
#         'Chan-only': {'est_traj': ..., 'true_traj': ..., 'total_error': ..., 'solve_times': ...},
#         'LM-only':   {...},
#         'Chan+LM':   {...}
#     }
#     """
#     print("\n[画图模式] 正在运行算法对比分析（单 UAV）...")

#     algorithms = ['Chan-only', 'LM-only', 'Chan+LM']
#     uav_id = list(uav_true_positions.keys())[0]
#     true_traj = uav_true_positions[uav_id]
#     n_points = len(true_traj)

#     algorithm_results = {}

#     for algorithm in algorithms:
#         est_traj = np.zeros_like(true_traj)
#         solve_times = np.zeros(n_points)
#         tdoa_measurements = calculate_tdoa_measurements_vectorized(all_anchors, true_traj, ref_idx, noise_std)

#         prev_est = None

#         for i, (true_pos, tdoa_meas) in enumerate(zip(true_traj, tdoa_measurements)):
#             start_time = time.perf_counter()

#             try:
#                 if algorithm == 'Chan-only':
#                     pos = chan_initial(all_anchors, tdoa_meas, ref_idx)
#                 elif algorithm == 'LM-only':
#                     initial_guess = prev_est if prev_est is not None else np.mean(all_anchors, axis=0)
#                     pos, _ = newton_tdoa_solver_lm(all_anchors, tdoa_meas, initial_guess, ref_idx)
#                 else:  # Chan+LM
#                     initial_guess = chan_initial(all_anchors, tdoa_meas, ref_idx) if prev_est is None else prev_est
#                     pos, _ = newton_tdoa_solver_lm(all_anchors, tdoa_meas, initial_guess, ref_idx)

#                 if pos is None or np.any(np.isnan(pos)) or np.any(np.isinf(pos)):
#                     pos = prev_est if prev_est is not None else np.mean(all_anchors, axis=0)
#                     print(f"[警告] {algorithm} 算法在 {uav_id} 的第 {i} 点求解失败，使用备用值")

#             except Exception as e:
#                 pos = prev_est if prev_est is not None else np.mean(all_anchors, axis=0)
#                 print(f"[异常] {algorithm} 在 {uav_id} 第 {i} 点异常：{e}，使用备用值")

#             solve_time = time.perf_counter() - start_time
#             prev_est = pos
#             est_traj[i] = pos
#             solve_times[i] = solve_time

#         total_error = np.linalg.norm(est_traj - true_traj, axis=1)

#         algorithm_results[algorithm] = {
#             'est_traj': est_traj,
#             'true_traj': true_traj,
#             'solve_times': solve_times,
#             'total_error': total_error
#         }

#     print("[画图模式] 算法对比完成。")
#     return algorithm_results


# ==============================================================================
# ================================ 主函数入口 =====================================
# ==============================================================================
def main():
    # === 全局参数定义 ===
    REF_STATION_INDEX = 0
    TDOA_NOISE_STD = 1.5  # 固定噪声标准差
    OPTIMIZE_EVERY_N_STEPS = 100  # 优化频率

    # TDOA求解算法选择
    # 可选项: 'newton' (原始牛顿法), 'lm' (Levenberg-Marquardt), 'chan+newton', 'chan+lm'
    TDOA_SOLVER_ALGORITHM = 'chan+lm'  # 默认使用改进的Levenberg-Marquardt算法
    
    # # 差分进化算法参数
    DE_PARAMS = {
        'NP': 15,           # 种群大小
        'G_max': 100,       # 最大代数
        'F0': 0.5,          # 初始缩放因子
        'patience': 20,     # 早停耐心值
        'tolerance': 1e-4   # 收敛容忍度
    } #15.31m
    # DE_PARAMS ={'NP': 10, 'G_max': 80, 'F0': 0.4, 'patience': 15, 'tolerance': 1e-3} 15.54m
    #DE_PARAMS ={'NP': 20, 'G_max': 150, 'F0': 0.7, 'patience': 30, 'tolerance': 1e-5} #14.80m
    # DE_PARAMS ={'NP': 30, 'G_max': 200, 'F0': 0.9, 'patience': 25, 'tolerance': 1e-6}15.04m
    # DE_PARAMS = {'NP': 12, 'G_max': 120, 'F0': 0.6, 'patience': 10, 'tolerance': 1e-3} 14.94m
   
    # 物理约束参数
    CONSTRAINTS = {
        'search_space': {
            'x': [400000, 480000], 
            'y': [4080000, 4150000], 
            'z': [500, 15000]
        },
        'forbidden_zones': [
            {'type': 'box', 'bounds': ([440000, 4090000, -100], [450000, 4100000, 300])}
        ],
        'Vmax': 15.0,  # 无人机最大飞行速度 (米/秒)
        'dt': 1.0      # 时间步长(秒), 稍后会从数据中自动计算
    }
    
    # === 数据加载 ===
    SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
    DATA_FILE_NAME = "uav_traj_with_formation_curve_path1(uav_num=5).csv"
    formation_data_path = os.path.join(SCRIPT_DIR, DATA_FILE_NAME)
    
    print("="*80)
    print("TDOA Positioning System - Formation Analysis with 4 Fixed + 2 Mobile Stations")
    print("="*80)
    
    if not os.path.exists(formation_data_path):
        print(f"Data file not found: {formation_data_path}")
        return
    
    # --- 1. 初始化和加载数据 ---
    # 生成基于日期时间的输出文件夹名称
    from datetime import datetime
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir_name = f"Loc_result/{current_time}_ref={REF_STATION_INDEX}_algo={TDOA_SOLVER_ALGORITHM}"
    output_dir = os.path.join(SCRIPT_DIR, output_dir_name)
    os.makedirs(output_dir, exist_ok=True)
    
    all_anchors, uav_true_positions, timestamps, formation_labels = load_data(formation_data_path)
    CONSTRAINTS['dt'] = np.mean(np.diff(timestamps)) if len(timestamps) > 1 else 1.0
    
    print(f"Data loaded successfully: {len(all_anchors)} monitors, {len(uav_true_positions)} target UAVs.")
    print(f"Using Monitor #{REF_STATION_INDEX + 1} as the reference station.")
    
    # --- 2. 初始化变量 ---
    fixed_anchors = all_anchors[:4]
    uav_anchors = all_anchors[4:6]
    uav_ids = list(uav_true_positions.keys())
    num_time_steps = len(timestamps)
    
    all_est_trajs = {uav_id: [] for uav_id in uav_ids}
    all_solve_times = {uav_id: [] for uav_id in uav_ids}
    prev_estimates = {uav_id: None for uav_id in uav_ids}
    
    uav5_trajectory_history = []
    uav6_trajectory_history = []
    
    optimizer_start_pos = uav_anchors.copy()
    optimizer_target_pos = uav_anchors.copy()
    steps_since_last_opt = 0
    
    # --- 3. 主循环：使用单一进度条 ---
    print(f"\n开始进行 {num_time_steps} 个时间步的TDOA定位...")
    
    # 主循环进度条
    with tqdm(total=num_time_steps, desc="TDOA定位进度", unit="步") as pbar:
        for time_idx in range(num_time_steps):
            
            # a. 周期性地执行观测站布局优化
            if time_idx > 0 and time_idx % OPTIMIZE_EVERY_N_STEPS == 0:
                pbar.set_description(f"[时间步 {time_idx}] 执行观测站布局优化")
                
                # 构建当前时刻的动态空域网格
                current_target_positions = np.array([uav_true_positions[uav_id][time_idx] for uav_id in uav_ids])
                dynamic_airspace_grid = generate_dynamic_airspace_grid(current_target_positions, grid_density=5)
                
                # 执行DE优化
                optimizer_start_pos = uav_anchors.copy()
                opt_results = run_adaptive_de_optimization(fixed_anchors, optimizer_start_pos, DE_PARAMS, CONSTRAINTS, dynamic_airspace_grid)
                optimizer_target_pos = opt_results['best_positions']
                steps_since_last_opt = 0
                
                pbar.write(f"优化完成。新目标位置: UAV5: {np.round(optimizer_target_pos[0], 2).tolist()}, UAV6: {np.round(optimizer_target_pos[1], 2).tolist()}")
                pbar.set_description("TDOA定位进度")

            # b. 更新优化无人机位置（考虑最大速度约束的线性插值）
            if time_idx > 0:
                # 计算时间步长
                dt = CONSTRAINTS['dt']
                max_distance = CONSTRAINTS['Vmax'] * dt  # 最大移动距离

                # 计算目标位置与当前位置的距离
                distance_to_target = np.linalg.norm(optimizer_target_pos - uav_anchors, axis=1)

                # 对每个移动基站分别处理速度约束
                new_uav_anchors = np.zeros_like(uav_anchors)
                for i in range(len(uav_anchors)):
                    if distance_to_target[i] <= max_distance:
                        # 如果距离小于最大移动距离，直接到达目标位置
                        new_uav_anchors[i] = optimizer_target_pos[i]
                    else:
                        # 如果距离大于最大移动距离，按最大速度移动
                        direction = (optimizer_target_pos[i] - uav_anchors[i]) / distance_to_target[i]
                        new_uav_anchors[i] = uav_anchors[i] + direction * max_distance

                uav_anchors = new_uav_anchors
            
            steps_since_last_opt += 1
            
            # c. 记录优化无人机轨迹
            current_anchors = np.vstack((fixed_anchors, uav_anchors))
            uav5_trajectory_history.append(uav_anchors[0])
            uav6_trajectory_history.append(uav_anchors[1])
            
            # d. 对每个目标无人机进行TDOA定位
            for uav_id in uav_ids:
                true_pos = uav_true_positions[uav_id][time_idx]
                
                # 生成TDOA测量值
                tdoa_meas = generate_tdoa_measurements(current_anchors, true_pos, REF_STATION_INDEX, TDOA_NOISE_STD)
                
                # 设置初始猜测
                if prev_estimates[uav_id] is not None:
                    initial_guess_for_newton = prev_estimates[uav_id]
                else:
                    initial_guess_for_newton = true_pos + np.random.normal(0, 50, 3)

                # 根据选择的算法进行求解
                start_solve_time = time.time()

                if TDOA_SOLVER_ALGORITHM == 'newton':
                    # 原始牛顿法
                    est_pos = newton_tdoa_solver(current_anchors, tdoa_meas, initial_guess_for_newton, REF_STATION_INDEX)

                elif TDOA_SOLVER_ALGORITHM == 'lm':
                    # Levenberg-Marquardt算法
                    est_pos, _ = newton_tdoa_solver_lm(current_anchors, tdoa_meas, initial_guess_for_newton, REF_STATION_INDEX)

                elif TDOA_SOLVER_ALGORITHM == 'chan+newton':
                    # Chan算法 + 牛顿法
                    try:
                        chan_guess = chan_initial(current_anchors, tdoa_meas, REF_STATION_INDEX)
                        est_pos = newton_tdoa_solver(current_anchors, tdoa_meas, chan_guess, REF_STATION_INDEX)
                    except:
                        # 如果Chan算法失败，使用默认初始猜测
                        est_pos = newton_tdoa_solver(current_anchors, tdoa_meas, initial_guess_for_newton, REF_STATION_INDEX)

                elif TDOA_SOLVER_ALGORITHM == 'chan+lm':
                    # Chan算法 + Levenberg-Marquardt算法
                    try:
                        chan_guess = chan_initial(current_anchors, tdoa_meas, REF_STATION_INDEX)
                        est_pos, _ = newton_tdoa_solver_lm(current_anchors, tdoa_meas, chan_guess, REF_STATION_INDEX)
                    except:
                        # 如果Chan算法失败，使用默认初始猜测
                        est_pos, _ = newton_tdoa_solver_lm(current_anchors, tdoa_meas, initial_guess_for_newton, REF_STATION_INDEX)

                else:
                    # 默认使用原始牛顿法
                    est_pos = newton_tdoa_solver(current_anchors, tdoa_meas, initial_guess_for_newton, REF_STATION_INDEX)

                solve_time = time.time() - start_solve_time
                
                # 记录结果
                all_est_trajs[uav_id].append(est_pos)
                all_solve_times[uav_id].append(solve_time)
                prev_estimates[uav_id] = est_pos
            
            # 更新进度条
            pbar.update(1)
    
    print(f"\n已完成所有 {len(uav_ids)} 个UAV在 {num_time_steps} 个时间步的TDOA定位。")
    # --- 4. 结果分析、保存和可视化 ---
    print(f"\n{'='*50}\n单架无人机性能分析与结果保存:\n{'='*50}")
    
    # 准备移动基站结果数据
    mobile_results = {}
    for uav_id in uav_ids:
        true_traj = uav_true_positions[uav_id]
        est_traj = np.array(all_est_trajs[uav_id])
        total_error = np.linalg.norm(true_traj - est_traj, axis=1)
        formation_stats = analyze_formation_errors(true_traj, est_traj, formation_labels)
        
        mobile_results[uav_id] = {
            'est_traj': est_traj,
            'solve_times': all_solve_times[uav_id],
            'mean_error': np.mean(total_error),
            'mean_time': np.mean(all_solve_times[uav_id]),
            'formation_stats': formation_stats,
            'total_error': total_error
        }
    
    # 运行6固定基站对比分析
    six_fixed_results = run_six_fixed_stations_comparison(
        all_anchors, uav_true_positions, formation_labels, REF_STATION_INDEX, TDOA_NOISE_STD
    )
    
    # 运行算法对比分析
    algorithm_results = run_algorithm_comparison(
        all_anchors, uav_true_positions, REF_STATION_INDEX, TDOA_NOISE_STD
    )
    
    #绘制三种算法二维误差对比图
    plot_2d_error_comparison(algorithm_results, uav_ids, save_path=os.path.join(output_dir, "2d_error_comparison.png"))

    # 创建性能分析报告文件
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = os.path.join(output_dir, f"comprehensive_analysis_report_{timestamp}.txt")
    
    with open(report_filename, 'w', encoding='utf-8') as report_file:
        # 写入报告头部
        report_file.write("="*80 + "\n")
        report_file.write("TDOA定位系统综合性能分析报告\n")
        report_file.write("="*80 + "\n")
        report_file.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        report_file.write(f"数据文件: {DATA_FILE_NAME}\n")
        report_file.write("\n" + "="*80 + "\n")
        report_file.write("实验参数配置\n")
        report_file.write("="*80 + "\n")
        
        # 基本参数
        report_file.write("基本参数:\n")
        report_file.write(f"  参考站索引: {REF_STATION_INDEX} (基站 #{REF_STATION_INDEX + 1})\n")
        report_file.write(f"  TDOA噪声标准差: {TDOA_NOISE_STD} m\n")
        report_file.write(f"  优化周期: 每 {OPTIMIZE_EVERY_N_STEPS} 个时间步\n")
        report_file.write(f"  处理的无人机数量: {len(uav_ids)}\n")
        report_file.write(f"  总时间步数: {num_time_steps}\n")
        report_file.write(f"  时间步长: {CONSTRAINTS['dt']:.2f} 秒\n")
        
        # DE优化算法参数
        report_file.write("\nDE优化算法参数:\n")
        report_file.write(f"  种群大小 (NP): {DE_PARAMS['NP']}\n")
        report_file.write(f"  最大迭代数 (G_max): {DE_PARAMS['G_max']}\n")
        report_file.write(f"  变异因子 (F0): {DE_PARAMS['F0']}\n")
        report_file.write(f"  早停耐心值 (patience): {DE_PARAMS['patience']}\n")
        report_file.write(f"  收敛容忍度 (tolerance): {DE_PARAMS['tolerance']}\n")
        
        # 优化无人机约束条件
        report_file.write("\n优化无人机物理约束:\n")
        report_file.write(f"  搜索空间 X: {CONSTRAINTS['search_space']['x']} m\n")
        report_file.write(f"  搜索空间 Y: {CONSTRAINTS['search_space']['y']} m\n")
        report_file.write(f"  搜索空间 Z: {CONSTRAINTS['search_space']['z']} m\n")
        report_file.write(f"  最大飞行速度: {CONSTRAINTS['Vmax']} m/s\n")
        
        # 禁飞区信息
        if 'forbidden_zones' in CONSTRAINTS and CONSTRAINTS['forbidden_zones']:
            report_file.write("\n禁飞区设置:\n")
            for i, zone in enumerate(CONSTRAINTS['forbidden_zones']):
                if zone['type'] == 'box':
                    bounds = zone['bounds']
                    report_file.write(f"  禁飞区 {i+1} (矩形): X[{bounds[0][0]}, {bounds[1][0]}], "
                                    f"Y[{bounds[0][1]}, {bounds[1][1]}], Z[{bounds[0][2]}, {bounds[1][2]}] m\n")
        else:
            report_file.write("\n禁飞区设置: 无\n")
        
        # 基站配置信息
        report_file.write("\n基站配置:\n")
        report_file.write(f"  固定基站数量: 4\n")
        report_file.write(f"  移动基站数量: 2\n")
        report_file.write("  固定基站位置:\n")
        for i, anchor in enumerate(fixed_anchors):
            report_file.write(f"    基站{i+1}: [{anchor[0]:.1f}, {anchor[1]:.1f}, {anchor[2]:.1f}] m\n")
        
        report_file.write("  移动基站初始位置:\n")
        initial_uav_anchors = all_anchors[4:6]
        for i, anchor in enumerate(initial_uav_anchors):
            report_file.write(f"    UAV{i+5}: [{anchor[0]:.1f}, {anchor[1]:.1f}, {anchor[2]:.1f}] m\n")
        
        report_file.write("\n" + "="*80 + "\n\n")
        
        # 1. 原有的单架无人机分析
        report_file.write("1. 单架无人机性能分析\n")
        report_file.write("="*80 + "\n")
        
        for uav_id in uav_ids:
            # 保存CSV文件并同时输出到文件
            formation_stats = save_results_with_file_output(
                os.path.join(output_dir, f"{uav_id}_positioning_results_with_formation.csv"),
                uav_id, uav_true_positions[uav_id], all_est_trajs[uav_id],
                formation_labels, REF_STATION_INDEX, timestamps, all_solve_times[uav_id], report_file
            )
            
            # 分析误差并同时写入文件
            analyze_error_with_file(uav_true_positions[uav_id], all_est_trajs[uav_id], uav_id, report_file)
        
        # 2. 总体统计
        report_file.write("\n" + "="*80 + "\n")
        report_file.write("2. 4固定+2移动基站总体性能统计\n")
        report_file.write("="*80 + "\n")
        
        # 计算所有UAV的总体误差统计
        all_errors = []
        all_solve_times_flat = []
        for uav_id in uav_ids:
            all_errors.extend(mobile_results[uav_id]['total_error'])
            all_solve_times_flat.extend(mobile_results[uav_id]['solve_times'])
        
        all_errors = np.array(all_errors)
        all_solve_times_flat = np.array(all_solve_times_flat)
        
        report_file.write(f"所有UAV总体3D定位误差:\n")
        report_file.write(f"  平均误差: {np.mean(all_errors):.2f}m\n")
        report_file.write(f"  中位数误差: {np.median(all_errors):.2f}m\n")
        report_file.write(f"  最大误差: {np.max(all_errors):.2f}m\n")
        report_file.write(f"  误差标准差: {np.std(all_errors):.2f}m\n")
        report_file.write(f"\n所有UAV总体定位时间:\n")
        report_file.write(f"  平均定位时间: {np.mean(all_solve_times_flat)*1000:.2f}ms\n")
        report_file.write(f"  最大定位时间: {np.max(all_solve_times_flat)*1000:.2f}ms\n")
        report_file.write(f"  最小定位时间: {np.min(all_solve_times_flat)*1000:.2f}ms\n")
        
        # # 3. 6固定 vs 4固定+2移动对比 & 4. 算法对比
        # write_comparison_analysis_to_file(
        #     report_file, mobile_results, six_fixed_results, algorithm_results, 
        #     formation_labels, uav_ids
        # )
        # === 构造每时刻动态基站布局 ===
        dynamic_anchors_per_frame = prepare_dynamic_anchors(fixed_anchors, uav5_trajectory_history, uav6_trajectory_history)

        # === 基于 4 固定 + 2 移动 配置运行算法对比分析 ===
        mobile_algorithm_results = run_algorithm_comparison_with_dynamic_anchors(
            dynamic_anchors_per_frame,
            uav_true_positions,
            REF_STATION_INDEX,
            TDOA_NOISE_STD
        )

        # === 替代算法分析结果传入报告 ===
        write_comparison_analysis_to_file(
            report_file,
            mobile_results,
            six_fixed_results,
            mobile_algorithm_results,
            formation_labels,
            uav_ids
        )
        report_file.write(f"\n" + "="*80 + "\n")
        report_file.write(f"综合分析报告生成完成。\n")
        report_file.write(f"报告包含: 单机性能分析、基站配置对比、算法性能对比\n")
        report_file.write(f"="*80 + "\n")
    
    print(f"\n综合性能分析报告已保存至: {report_filename}")
    
    # --- 5. 生成可视化图表和视频 ---
    print(f"\n{'='*50}\n可视化生成:\n{'='*50}")
    
    # 创建具体的可视化图表
    print("正在生成轨迹对比图...")
    for uav_id in uav_ids:
        if uav_id in uav_true_positions and uav_id in all_est_trajs:
            # 确保数据是NumPy数组
            true_traj = np.array(uav_true_positions[uav_id])
            est_traj = np.array(all_est_trajs[uav_id])
            plot_trajectories(true_traj, est_traj, uav_id, output_dir)

    print("正在生成2D轨迹对比图（包含阵型信息）...")
    plot_all_uavs_2d_comparison_with_formation(all_est_trajs, uav_true_positions, formation_labels, output_dir)

    print("正在生成2D轨迹总览图...")
    plot_all_uavs_2d_comparison(uav_true_positions, all_est_trajs,
                                np.array(uav5_trajectory_history), np.array(uav6_trajectory_history),
                                all_anchors[:4], REF_STATION_INDEX, CONSTRAINTS)

    print("正在生成误差时间序列图...")
    plot_error_vs_time(uav_true_positions, all_est_trajs, timestamps, output_dir)

    print("正在生成基站布局误差对比图（4+2 vs 6）...")
    plot_error_comparison_between_station_layouts(mobile_results, six_fixed_results, output_dir)
    
    # print("正在生成三种算法误差对比图...")
    # algorithm_results = run_algorithm_comparison_for_plot(
    # all_anchors, uav_true_positions, ref_idx=0, noise_std=1.5
    # )
    # plot_solver_error_comparison(algorithm_results, output_dir)

    
 

    # print("正在创建仿真动画...")
    # create_simulation_animation(uav_true_positions, np.array(uav5_trajectory_history),
    #                            np.array(uav6_trajectory_history), all_anchors[:4],
    #                            output_dir, REF_STATION_INDEX)
    
    # # 添加XYZ三轴误差对比
    # plot_xyz_error_comparison(algorithm_results, uav_ids, output_dir)
    
    # # 创建轨迹动画视频
    # create_trajectory_video(
    #     output_dir, uav_true_positions, all_est_trajs, timestamps,
    #     formation_labels, all_anchors
    # )
    print("正在生成移动基站轨迹图...")
    plot_mobile_anchors_trajectory(uav5_trajectory_history, uav6_trajectory_history, output_dir)


    plot_solver_time_comparison(algorithm_results, output_dir)

    print(f"\n{'='*50}\n所有分析完成!\n{'='*50}")
    print(f"输出目录: {output_dir}")
    print(f"包含文件:")
    
    # 打印实验参数总结
    print("正在打印实验参数...")
    print_experiment_parameters_to_file(
        None, REF_STATION_INDEX, TDOA_NOISE_STD, OPTIMIZE_EVERY_N_STEPS,
        DE_PARAMS, CONSTRAINTS, DATA_FILE_NAME, uav_ids, num_time_steps,
        fixed_anchors, all_anchors[4:6], TDOA_SOLVER_ALGORITHM
    )
#误差分析
    print(f"\n实际生成的文件:")
    print(f"  - CSV结果文件 (每个UAV)")
    print(f"  - 综合分析报告 (TXT)")
    print(f"  - 3D轨迹对比图 (PNG) - 仅plot_trajectories")
    print(f"  - 其他可视化图表 (如果函数存在)")

    print(f"\n当前TDOA求解器配置:")
    print(f"  - 算法: {TDOA_SOLVER_ALGORITHM}")
    print(f"  - 全局参数: {TDOA_SOLVER_PARAMS}")
    print(f"  - 噪声标准差: {TDOA_NOISE_STD}m")
    print(f"  - 参考站: 基站#{REF_STATION_INDEX + 1}")

if __name__ == "__main__":
    main()
